#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻页配置修复
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pagination_logic():
    """测试翻页逻辑判断"""
    print("=" * 60)
    print("测试翻页逻辑判断")
    print("=" * 60)
    
    # 模拟不同的配置场景
    test_cases = [
        {
            "name": "滚动翻页 + 加载元素模式",
            "pagination_config": {
                "pagination_type": "滚动翻页",
                "enabled": True,
                "load_element_pattern": "#load{n}",
                "scroll_delay": 2000,
                "scroll_step": 1000,
                "max_scrolls": 15
            }
        },
        {
            "name": "滚动翻页 + 传统模式",
            "pagination_config": {
                "pagination_type": "滚动翻页",
                "enabled": True,
                "load_element_pattern": "",
                "scroll_delay": 2000,
                "scroll_step": 800,
                "max_scrolls": 10
            }
        },
        {
            "name": "点击翻页",
            "pagination_config": {
                "pagination_type": "点击翻页",
                "enabled": True,
                "next_button_selector": "a.next:not(.lose)",
                "max_pages": 10
            }
        },
        {
            "name": "禁用动态翻页",
            "pagination_config": {
                "pagination_type": "禁用动态翻页",
                "enabled": False
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        pagination_config = case['pagination_config']
        
        # 模拟修复后的条件判断
        if (pagination_config.get('enabled', False) and
            pagination_config.get('pagination_type') != '禁用动态翻页'):
            
            print("  ✅ 启用动态翻页模式")
            
            pagination_type = pagination_config.get('pagination_type', '点击翻页')
            print(f"  翻页类型: {pagination_type}")
            
            if pagination_type == '点击翻页':
                print("  → 使用点击翻页逻辑")
                print(f"    下一页按钮: {pagination_config.get('next_button_selector', 'a.next:not(.lose)')}")
                
            elif pagination_type == '滚动翻页':
                print("  → 使用滚动翻页逻辑")
                load_element_pattern = pagination_config.get('load_element_pattern', '')
                
                if load_element_pattern and '{n}' in load_element_pattern:
                    print(f"    ✅ 使用加载元素模式: {load_element_pattern}")
                    print("    → 调用 scroll_until_load_stops 方法")
                else:
                    print("    ✅ 使用传统滚动模式")
                    print("    → 调用 scroll_pagination 方法")
                    
            elif pagination_type == 'iframe翻页':
                print("  → 使用iframe翻页逻辑")
            else:
                print(f"  ❌ 不支持的翻页类型: {pagination_type}")
        else:
            print("  ❌ 使用传统翻页模式")

def create_correct_config():
    """创建正确的配置文件"""
    print("\n" + "=" * 60)
    print("创建正确的配置文件")
    print("=" * 60)
    
    # 为 https://www.shrd.gov.cn/n8347/n8378/index.html 创建正确配置
    correct_config = {
        "input_url": "https://www.shrd.gov.cn/n8347/n8378/index.html",
        "base_url": "https://www.shrd.gov.cn",
        "max_pages": 15,
        "list_container_selector": "body",
        "article_item_selector": "a[href*='/n8347/']",
        "title_selector": "",
        "content_selector": ".content",
        "date_selector": ".date",
        "source_selector": ".source",
        "save_dir": "./articles",
        "pagination_config": {
            "pagination_type": "滚动翻页",  # 确保使用正确的类型名称
            "enabled": True,
            "scroll_container_selector": "body",
            "scroll_step": 1000,
            "scroll_delay": 2000,
            "max_scrolls": 15,
            "load_indicator_selector": "",
            "scroll_timeout": 10000,
            "height_tolerance": 50,
            "load_element_pattern": "#load{n}"  # 关键配置
        },
        "browser": "Chrome",
        "headless": True,
        "window_size": "1200,800",
        "page_load_strategy": "normal",
        "collect_links": True,
        "mode": "平衡模式",
        "export_filename": "shrd_articles",
        "file_format": "CSV",
        "max_workers": 3,
        "retry": 3,
        "interval": 1.0
    }
    
    # 保存配置
    config_file = "correct_pagination_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(correct_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 正确配置已保存到: {config_file}")
    print("关键配置:")
    print(f"  翻页类型: {correct_config['pagination_config']['pagination_type']}")
    print(f"  启用状态: {correct_config['pagination_config']['enabled']}")
    print(f"  加载元素模式: {correct_config['pagination_config']['load_element_pattern']}")
    
    return config_file

def verify_config_logic():
    """验证配置逻辑"""
    print("\n" + "=" * 60)
    print("验证配置逻辑")
    print("=" * 60)
    
    config_file = "correct_pagination_config.json"
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        pagination_config = config.get('pagination_config', {})
        
        print("配置验证:")
        print(f"  pagination_config.enabled: {pagination_config.get('enabled', False)}")
        print(f"  pagination_config.pagination_type: {pagination_config.get('pagination_type', '未设置')}")
        
        # 模拟修复后的条件判断
        condition1 = pagination_config.get('enabled', False)
        condition2 = pagination_config.get('pagination_type') != '禁用动态翻页'
        
        print(f"  条件1 (enabled): {condition1}")
        print(f"  条件2 (not 禁用): {condition2}")
        print(f"  总条件: {condition1 and condition2}")
        
        if condition1 and condition2:
            print("\n✅ 将使用动态翻页模式")
            
            pagination_type = pagination_config.get('pagination_type', '点击翻页')
            if pagination_type == '滚动翻页':
                print("✅ 匹配滚动翻页逻辑")
                
                load_element_pattern = pagination_config.get('load_element_pattern', '')
                if load_element_pattern and '{n}' in load_element_pattern:
                    print(f"✅ 将使用 scroll_until_load_stops 方法")
                    print(f"   加载元素模式: {load_element_pattern}")
                else:
                    print("✅ 将使用传统 scroll_pagination 方法")
            else:
                print(f"❌ 意外的翻页类型: {pagination_type}")
        else:
            print("\n❌ 将使用传统翻页模式")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print("开始测试翻页配置修复...")
    
    # 测试翻页逻辑
    test_pagination_logic()
    
    # 创建正确配置
    create_correct_config()
    
    # 验证配置逻辑
    verify_config_logic()
    
    print("\n" + "=" * 60)
    print("修复总结")
    print("=" * 60)
    print("✅ 关键修复:")
    print("1. 移除了对旧 dynamic_pagination_type 的依赖")
    print("2. 优先使用新的 pagination_config")
    print("3. 确保翻页类型名称完全匹配")
    
    print("\n🔧 现在的逻辑:")
    print("- 只检查 pagination_config.enabled 和 pagination_type")
    print("- 滚动翻页 + #load{n} → scroll_until_load_stops")
    print("- 滚动翻页 + 无模式 → scroll_pagination")
    print("- 点击翻页 → click_pagination")
    
    print("\n🚀 请重新测试GUI功能!")

if __name__ == "__main__":
    main()
