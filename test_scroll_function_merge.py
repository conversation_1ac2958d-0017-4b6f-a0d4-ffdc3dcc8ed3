#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滚动翻页函数合并
"""

import sys
import os
import inspect

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_function_merge():
    """测试函数合并结果"""
    print("=" * 60)
    print("测试滚动翻页函数合并结果")
    print("=" * 60)
    
    try:
        from PaginationHandler import PaginationHandler
        
        # 检查PaginationHandler类的方法
        methods = [method for method in dir(PaginationHandler) if not method.startswith('_')]
        print("PaginationHandler 类的公共方法:")
        for method in sorted(methods):
            print(f"  - {method}")
        
        print("\n" + "=" * 40)
        print("关键方法检查")
        print("=" * 40)
        
        # 检查是否存在 scroll_pagination 方法
        if hasattr(PaginationHandler, 'scroll_pagination'):
            print("✅ scroll_pagination 方法存在")
            
            # 获取方法签名
            method = getattr(PaginationHandler, 'scroll_pagination')
            sig = inspect.signature(method)
            print(f"方法签名: {sig}")
            
            # 检查参数
            params = list(sig.parameters.keys())
            print("参数列表:")
            for param in params:
                param_obj = sig.parameters[param]
                default = param_obj.default
                if default == inspect.Parameter.empty:
                    print(f"  - {param} (必需)")
                else:
                    print(f"  - {param} = {default}")
                    
        else:
            print("❌ scroll_pagination 方法不存在")
        
        # 检查是否还存在旧的 scroll_until_load_stops 方法
        if hasattr(PaginationHandler, 'scroll_until_load_stops'):
            print("❌ scroll_until_load_stops 方法仍然存在（应该已被删除）")
        else:
            print("✅ scroll_until_load_stops 方法已成功删除")
            
        print("\n" + "=" * 40)
        print("参数兼容性测试")
        print("=" * 40)
        
        # 测试参数组合
        test_cases = [
            {
                "name": "加载元素模式",
                "params": {
                    "load_element_pattern": "#load{n}",
                    "max_loads": 10,
                    "scroll_delay": 2000
                }
            },
            {
                "name": "传统滚动模式",
                "params": {
                    "scroll_container_selector": "body",
                    "max_scrolls": 20,
                    "scroll_delay": 1000
                }
            }
        ]
        
        for case in test_cases:
            print(f"\n测试案例: {case['name']}")
            params = case['params']
            
            # 检查参数是否与方法签名兼容
            method = getattr(PaginationHandler, 'scroll_pagination')
            sig = inspect.signature(method)
            
            compatible_params = []
            incompatible_params = []
            
            for param_name in params.keys():
                if param_name in sig.parameters:
                    compatible_params.append(param_name)
                else:
                    incompatible_params.append(param_name)
            
            if compatible_params:
                print(f"  ✅ 兼容参数: {', '.join(compatible_params)}")
            if incompatible_params:
                print(f"  ❌ 不兼容参数: {', '.join(incompatible_params)}")
            
            # 判断会使用哪种模式
            if params.get('load_element_pattern') and '{n}' in params.get('load_element_pattern', ''):
                print("  → 将使用加载元素模式")
            else:
                print("  → 将使用传统滚动模式")
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

def test_gui_compatibility():
    """测试GUI兼容性"""
    print("\n" + "=" * 60)
    print("测试GUI兼容性")
    print("=" * 60)
    
    try:
        # 检查GUI文件是否正确更新
        with open('crawler_gui_2.py', 'r', encoding='utf-8') as f:
            gui_content = f.read()
        
        # 检查是否还有对旧函数的调用
        if 'scroll_until_load_stops' in gui_content:
            print("❌ GUI代码中仍然包含对 scroll_until_load_stops 的调用")
            
            # 找到调用位置
            lines = gui_content.split('\n')
            for i, line in enumerate(lines):
                if 'scroll_until_load_stops' in line:
                    print(f"  第{i+1}行: {line.strip()}")
        else:
            print("✅ GUI代码中已移除对 scroll_until_load_stops 的调用")
        
        # 检查是否正确使用统一的 scroll_pagination
        if 'scroll_pagination(' in gui_content:
            print("✅ GUI代码中使用了 scroll_pagination 函数")
        else:
            print("❌ GUI代码中未找到对 scroll_pagination 的调用")
            
        # 检查参数传递
        if 'load_element_pattern' in gui_content:
            print("✅ GUI代码中包含 load_element_pattern 参数")
        else:
            print("❌ GUI代码中缺少 load_element_pattern 参数")
            
    except Exception as e:
        print(f"❌ GUI兼容性测试出错: {e}")

def main():
    """主函数"""
    print("开始测试滚动翻页函数合并...")
    
    # 测试函数合并
    test_function_merge()
    
    # 测试GUI兼容性
    test_gui_compatibility()
    
    print("\n" + "=" * 60)
    print("合并总结")
    print("=" * 60)
    print("🎯 合并目标:")
    print("1. 将 scroll_pagination 和 scroll_until_load_stops 合并为一个函数")
    print("2. 统一函数名为 scroll_pagination")
    print("3. 支持两种模式：传统滚动 + 加载元素模式")
    print("4. 保持向后兼容性")
    
    print("\n✅ 预期结果:")
    print("- 只有一个 scroll_pagination 函数")
    print("- 根据 load_element_pattern 参数自动选择模式")
    print("- GUI 使用统一的函数调用")
    print("- 所有现有功能正常工作")
    
    print("\n🚀 下一步:")
    print("请测试GUI功能，确认滚动翻页工作正常！")

if __name__ == "__main__":
    main()
