import selenium_diver_change
import crawler



input_url= "https://www.rd.suzhou.gov.cn/lfgz/012001/secondPage.html"
base_url= "https://www.rd.suzhou.gov.cn/lfgz/012001/"
url_mode="base_url"
list_container_selector=".ewb-intro-bd ul"
article_item_selector=".ewb-intro-bd ul li"
content_selectors=[  # 移除这里的逗号
    ".ck-content"
]
date_selector="div#.width (4 children)"
source_selector="div#.pub"
log_callback=None
page_suffix="index_{n}.html"
browser="firefox"
diver=None

driver = selenium_diver_change.get_driver(browser=browser, diver=diver)
processed_titles = set()
all_article_info = []
found_urls = set()   

    
# 替换最后的print语句为以下代码：
if __name__ == "__main__":
    # 修改为接收4个返回值
    article_elements, page_title, article_links, article_titles = crawler.get_article_links(driver, input_url, list_container_selector, article_item_selector)

    # 测试第一个链接
    if article_links:
        test_href = article_links[0]
        print(f"测试第一个链接: {test_href}")
        print("完整URL:", crawler.get_full_link(test_href, input_url, base_url, url_mode))
    else:
        print("未找到任何文章链接")
    
    driver.quit()  # 关闭浏览器