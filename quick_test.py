#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试翻页修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_selector_fix():
    """测试选择器修复"""
    
    # 模拟 _click_next_page_selenium 函数的逻辑
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.firefox.options import Options
    import time
    
    options = Options()
    options.add_argument('--headless')
    
    driver = None
    try:
        driver = webdriver.Firefox(options=options)
        
        url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
        driver.get(url)
        time.sleep(3)
        
        print(f"页面标题: {driver.title}")
        
        # 测试修复后的选择器列表
        next_selectors = [
            (By.CSS_SELECTOR, 'a.next:not(.lose)'),  # 修复后的选择器
            (By.CSS_SELECTOR, '.next:not(.lose)'),   # 原始选择器
            (By.CSS_SELECTOR, '.js_pageI:not(.cur)'),
        ]
        
        print("\n=== 测试修复后的选择器逻辑 ===")
        
        found_working_selector = False
        
        for by_type, selector in next_selectors:
            try:
                elements = driver.find_elements(by_type, selector)
                print(f"\n选择器: {selector}")
                print(f"找到元素数量: {len(elements)}")
                
                for i, element in enumerate(elements):
                    if element.is_displayed() and element.is_enabled():
                        class_attr = element.get_attribute('class') or ''
                        text = element.text.strip()
                        
                        print(f"  元素 {i+1}: '{text}' (类: {class_attr})")
                        
                        # 检查是否是有效的下一页按钮
                        if 'cur' not in class_attr and 'lose' not in class_attr:
                            print(f"  ✅ 这是一个有效的翻页按钮！")
                            
                            if not found_working_selector:
                                print(f"  🎯 第一个工作的选择器: {selector}")
                                found_working_selector = True
                                
                                # 尝试点击
                                try:
                                    element.click()
                                    time.sleep(2)
                                    print(f"  ✅ 点击成功！")
                                    
                                    # 检查是否到了第2页
                                    try:
                                        current_page = driver.find_element(By.CSS_SELECTOR, '.cur').text
                                        print(f"  当前页码: {current_page}")
                                    except:
                                        print(f"  无法获取当前页码")
                                        
                                    return True
                                except Exception as e:
                                    print(f"  ❌ 点击失败: {e}")
                        else:
                            print(f"  ⚠️  跳过 (类包含 cur 或 lose): {class_attr}")
                            
            except Exception as e:
                print(f"选择器 {selector} 出错: {e}")
        
        if not found_working_selector:
            print("❌ 没有找到工作的选择器")
            return False
            
    except Exception as e:
        print(f"测试出错: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()
    
    return found_working_selector

if __name__ == "__main__":
    print("🔧 快速测试翻页修复")
    print("=" * 40)
    
    success = test_selector_fix()
    
    if success:
        print("\n🎉 修复成功！翻页功能应该可以正常工作了。")
    else:
        print("\n❌ 仍有问题，需要进一步调试。")
