date_selector=meta[name="PubDate"], .attribute span:nth-child(3)
source_selector=meta[name="ContentSource"], .attribute span:nth-child(2)
content_selectors=.detailsMain, .trs_editor_view
tilte_selector=meta[name="ArticleTitle"], .xw_title

======== 简要描述 =========
该网站是一个政府新闻类网站，结构清晰，采用传统的HTML布局。文章页面包含完整的元信息（标题、日期、来源等）和正文内容。翻页规则通过"上一篇/下一篇"链接实现，但未显示总页数。容器定位信度高，关键信息同时存在于meta标签和可见DOM中，提供双重保障。

======= 容器分析结果 ======
1. 日期同时存在于meta标签和可见DOM的发布时间span中
2. 文章来源同样有meta和DOM双重来源
3. 正文内容位于detailsMain容器内，包含带格式的trs_editor_view
4. 标题在meta和h1标题标签中均有体现
5. 所有选择器都指向唯一确定的元素，无歧义