#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的动态翻页功能
验证wait_time参数问题已解决，并且文章提取正常工作
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_fixed_dynamic_pagination():
    """测试修复后的动态翻页功能"""
    print("=" * 60)
    print("测试修复后的动态翻页功能")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        async with async_playwright() as p:
            # 启动浏览器
            browser, context, page = await PaginationHandler.launch_browser(p, headless=True)
            
            try:
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                # 测试URL
                test_url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
                
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                
                print("页面加载完成，开始测试动态翻页...")
                
                # 准备文章提取配置（与GUI中的配置一致）
                extract_config = {
                    'list_container_selector': '.search-result',  # 搜索结果容器
                    'article_item_selector': '.search-item a',    # 文章链接选择器
                    'title_selector': None,                       # 使用默认标题提取
                    'save_dir': "测试动态翻页",
                    'page_title': "测试页面",
                    'classid': "test",
                    'base_url': "http://www.gysrd.gov.cn",
                    'url_mode': 'relative'
                }
                
                # 测试修复后的click_pagination方法
                print("测试click_pagination方法（修复wait_time参数问题）...")
                pages_processed = await handler.click_pagination(
                    next_button_selector="a.next:not(.lose)",     # 下一页按钮选择器
                    max_pages=3,                                  # 最大翻页数
                    content_ready_selector=None,                  # 内容就绪选择器
                    timeout=10000,                                # 超时时间
                    wait_after_click=2000,                        # 点击后等待时间（修复：不再是wait_time）
                    disabled_check=True,                          # 检查按钮禁用状态
                    extract_articles_config=extract_config        # 文章提取配置
                )
                
                print(f"✅ 翻页处理完成！处理了 {pages_processed} 页")
                
                # 获取提取到的文章
                all_articles = handler.get_all_articles()
                print(f"✅ 总共收集到 {len(all_articles)} 篇文章")
                
                # 显示前几篇文章的信息
                if all_articles:
                    print("\n前3篇文章信息:")
                    for i, article in enumerate(all_articles[:3]):
                        title, href, save_dir, page_title, page_url, classid = article
                        print(f"  {i+1}. 标题: {title[:50]}...")
                        print(f"     链接: {href}")
                        print(f"     完整URL: {'是' if href.startswith('http') else '否'}")
                        print()
                
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_parameter_compatibility():
    """测试参数兼容性"""
    print("=" * 60)
    print("测试参数兼容性")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        async with async_playwright() as p:
            browser, context, page = await PaginationHandler.launch_browser(p, headless=True)
            
            try:
                handler = PaginationHandler.PaginationHandler(page)
                
                # 测试所有参数都正确传递
                print("测试click_pagination方法参数...")
                
                # 检查方法签名
                import inspect
                sig = inspect.signature(handler.click_pagination)
                params = list(sig.parameters.keys())
                
                expected_params = [
                    'self', 'next_button_selector', 'max_pages', 
                    'content_ready_selector', 'timeout', 'wait_after_click', 
                    'disabled_check', 'extract_articles_config'
                ]
                
                print(f"方法参数: {params}")
                
                # 检查是否包含所有期望的参数
                missing_params = [p for p in expected_params if p not in params]
                extra_params = [p for p in params if p not in expected_params]
                
                if missing_params:
                    print(f"❌ 缺少参数: {missing_params}")
                    return False
                
                if extra_params:
                    print(f"⚠️  额外参数: {extra_params}")
                
                print("✅ 参数检查通过！")
                
                # 检查是否不再有wait_time参数
                if 'wait_time' in params:
                    print("❌ 仍然存在wait_time参数，应该是wait_after_click")
                    return False
                else:
                    print("✅ wait_time参数问题已修复！")
                
                return True
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 参数兼容性测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试修复后的动态翻页功能...")
    print()
    
    success_count = 0
    total_tests = 2
    
    # 测试参数兼容性
    if await test_parameter_compatibility():
        success_count += 1
    print()
    
    # 测试修复后的动态翻页
    if await test_fixed_dynamic_pagination():
        success_count += 1
    print()
    
    print("=" * 60)
    print(f"测试完成！成功: {success_count}/{total_tests}")
    if success_count == total_tests:
        print("🎉 所有测试通过！动态翻页功能修复成功！")
        print("\n主要修复:")
        print("✅ 修复了wait_time参数错误（改为wait_after_click）")
        print("✅ 增强了click_pagination方法，支持自动文章提取")
        print("✅ 改进了参数传递和配置管理")
        print("✅ 增加了详细的日志输出")
    else:
        print(f"⚠️  有 {total_tests - success_count} 个测试失败")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
