#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的加载元素翻页功能
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_load_element_pagination():
    """测试加载元素翻页功能"""
    print("=" * 60)
    print("测试加载元素翻页功能")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        # 测试URL - 使用您提到的网站
        test_url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
        
        async with async_playwright() as p:
            # 启动浏览器（非无头模式以便观察）
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                print("页面加载完成")
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                print("\n开始测试加载元素翻页功能...")
                
                # 配置文章提取参数
                extract_config = {
                    'list_container_selector': '.main',
                    'article_item_selector': '.clearfix.ty_list li a',
                    'title_selector': '',
                    'save_dir': '测试结果',
                    'page_title': '测试页面',
                    'classid': 'test'
                }
                
                # 测试新的滚动直到加载停止功能
                print("使用加载元素模式: #load{n}")
                load_count = await handler.scroll_until_load_stops(
                    load_element_pattern="#load{n}",  # 您提到的加载元素模式
                    max_loads=10,  # 最多加载10次
                    scroll_delay=2000,  # 滚动延迟2秒
                    scroll_step=800,  # 每次滚动800像素
                    extract_articles_config=extract_config
                )
                
                print(f"\n✅ 加载元素翻页测试完成")
                print(f"实际加载次数: {load_count}")
                
                # 获取收集到的文章
                all_articles = handler.get_all_articles()
                print(f"总共收集到 {len(all_articles)} 篇文章")
                
                # 显示前几篇文章的信息
                if all_articles:
                    print("\n前5篇文章:")
                    for i, article in enumerate(all_articles[:5]):
                        print(f"  {i+1}. {article.get('title', '无标题')} - {article.get('url', '无链接')}")
                
                # 等待观察
                print("\n等待15秒供观察...")
                await page.wait_for_timeout(15000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_traditional_scroll():
    """测试传统滚动翻页作为对比"""
    print("\n" + "=" * 60)
    print("测试传统滚动翻页（对比）")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        # 测试URL
        test_url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
        
        async with async_playwright() as p:
            # 启动浏览器
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                print("页面加载完成")
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                print("\n开始测试传统滚动翻页...")
                
                # 测试传统滚动翻页
                scroll_count = await handler.scroll_pagination(
                    scroll_container_selector="body",  # 使用body作为滚动容器
                    scroll_step=800,
                    scroll_delay=2000,
                    max_scrolls=5,
                    load_indicator_selector="",
                    scroll_timeout=10000,
                    height_tolerance=50
                )
                
                print(f"\n✅ 传统滚动翻页测试完成")
                print(f"实际滚动次数: {scroll_count}")
                
                # 等待观察
                print("\n等待10秒供观察...")
                await page.wait_for_timeout(10000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 传统滚动测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("开始测试新的加载元素翻页功能...")
    
    # 测试新的加载元素翻页
    await test_load_element_pagination()
    
    # 测试传统滚动翻页作为对比
    await test_traditional_scroll()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print("1. 新的加载元素翻页功能已实现")
    print("2. 支持 #load{n} 模式的动态加载检测")
    print("3. 当找不到下一个加载元素时自动停止")
    print("4. 在GUI中添加了加载元素模式配置选项")
    print("\n使用方法:")
    print("1. 在动态翻页设置中选择'滚动翻页'")
    print("2. 在'加载元素模式'中输入: #load{n}")
    print("3. 设置合适的滚动延迟和最大加载次数")
    print("4. 启动爬取任务")

if __name__ == "__main__":
    asyncio.run(main())
