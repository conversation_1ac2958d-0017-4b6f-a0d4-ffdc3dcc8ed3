#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分页调试工具
用于调试和测试分页功能
"""

import asyncio
from playwright.async_api import async_playwright
from PaginationHandler import PaginationHandler, launch_browser
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('PaginationDebug')

async def debug_pagination_on_page(url: str, selectors: list = None):
    """
    在指定页面上调试分页元素
    
    Args:
        url: 要调试的页面URL
        selectors: 要检查的选择器列表
    """
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        handler = PaginationHandler(page)
        
        try:
            logger.info(f"正在访问页面: {url}")
            await page.goto(url, timeout=60000, wait_until="domcontentloaded")
            
            # 等待页面稳定
            await page.wait_for_timeout(3000)
            
            # 调试分页元素
            debug_info = await handler.debug_pagination_elements(selectors)
            
            logger.info("=== 分页调试信息 ===")
            logger.info(f"页面URL: {debug_info['page_url']}")
            logger.info(f"页面标题: {debug_info['page_title']}")
            logger.info(f"找到的元素数量: {len(debug_info['found_elements'])}")
            
            for element in debug_info['found_elements']:
                logger.info(f"选择器: {element['selector']}")
                logger.info(f"  索引: {element['index']}")
                logger.info(f"  文本: '{element['text']}'")
                logger.info(f"  可见: {element['visible']}")
                logger.info(f"  启用: {element['enabled']}")
                logger.info("---")
            
            # 截图保存
            await page.screenshot(path="pagination_debug.png")
            logger.info("调试截图已保存为: pagination_debug.png")
            
            return debug_info
            
        except Exception as e:
            logger.error(f"调试过程中出错: {str(e)}")
            return None
        finally:
            await context.close()
            await browser.close()

async def test_pagination_with_debug(url: str, next_button_selector: str, max_pages: int = 2):
    """
    测试分页功能并提供详细调试信息
    
    Args:
        url: 测试页面URL
        next_button_selector: 下一页按钮选择器
        max_pages: 最大测试页数
    """
    async with async_playwright() as p:
        browser, context, page = await launch_browser(p, headless=False)
        handler = PaginationHandler(page)
        
        try:
            logger.info(f"正在访问页面: {url}")
            await page.goto(url, timeout=60000, wait_until="domcontentloaded")
            
            # 等待页面稳定
            await page.wait_for_timeout(3000)
            
            # 先调试分页元素
            logger.info("=== 开始调试分页元素 ===")
            debug_info = await handler.debug_pagination_elements([next_button_selector])
            
            if not debug_info['found_elements']:
                logger.warning(f"未找到选择器 '{next_button_selector}' 对应的元素")
                # 尝试查找其他可能的分页元素
                logger.info("尝试查找其他可能的分页元素...")
                debug_info = await handler.debug_pagination_elements()
                
                if debug_info['found_elements']:
                    logger.info("找到以下可能的分页元素:")
                    for element in debug_info['found_elements']:
                        if element['visible'] and element['enabled']:
                            logger.info(f"  推荐选择器: {element['selector']} (文本: '{element['text']}')")
                else:
                    logger.warning("页面上未找到任何分页元素")
                    return 0
            
            # 执行分页测试
            logger.info("=== 开始分页测试 ===")
            pages_processed = await handler.click_pagination(
                next_button_selector=next_button_selector,
                max_pages=max_pages,
                timeout=10000,  # 增加超时时间
                wait_after_click=2000  # 增加等待时间
            )
            
            logger.info(f"成功处理了 {pages_processed} 页")
            return pages_processed
            
        except Exception as e:
            logger.error(f"测试过程中出错: {str(e)}")
            return 0
        finally:
            await context.close()
            await browser.close()

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='分页调试工具')
    parser.add_argument('--url', type=str, 
                       default="http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign=",
                       help='要调试的页面URL')
    parser.add_argument('--selector', type=str, 
                       default="a.next:not(.lose)",
                       help='下一页按钮选择器')
    parser.add_argument('--max-pages', type=int, default=2,
                       help='最大测试页数')
    parser.add_argument('--debug-only', action='store_true',
                       help='仅调试分页元素，不执行分页测试')
    
    args = parser.parse_args()
    
    if args.debug_only:
        logger.info("=== 仅调试模式 ===")
        await debug_pagination_on_page(args.url)
    else:
        logger.info("=== 完整测试模式 ===")
        await test_pagination_with_debug(args.url, args.selector, args.max_pages)

if __name__ == "__main__":
    asyncio.run(main())
