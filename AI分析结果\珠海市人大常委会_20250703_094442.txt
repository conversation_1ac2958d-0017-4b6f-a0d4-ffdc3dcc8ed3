根据页面内容分析，以下是定位容器的CSS选择器：

date_selector=.n_new_xx_bt span
source_selector=
content_selectors=.n_new_xx_xw
tilte_selector=.n_new_xx_bt

======== 简要描述 =========
这是一个政府网站文章详情页，结构清晰规范。特点：
1. 标题、日期和正文都有明确的class容器
2. 文章来源未明确标注
3. 正文内容包含在TRS_Editor容器内
4. 单篇文章页面，无翻页需求
5. 容器定位置信度高，结构稳定

======= 容器分析结果 ======
date_selector=.n_new_xx_bt span
source_selector=
content_selectors=.n_new_xx_xw
tilte_selector=.n_new_xx_bt

解释说明：
1. 日期在标题span内，格式规范
2. 文章来源未找到明确标注
3. 正文容器包含所有内容，包括格式文本
4. 标题容器明确，包含完整标题文本
5. 政府网站结构稳定，选择器可靠性高