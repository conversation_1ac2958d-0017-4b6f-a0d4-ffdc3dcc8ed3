#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的爬虫模块
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_traditional_pagination():
    """测试传统分页功能"""
    print("=" * 50)
    print("测试传统分页功能")
    print("=" * 50)
    
    try:
        import crawler
        
        # 测试配置
        config = {
            'input_url': 'http://www.npc.gov.cn/npc/c12435/list.shtml',
            'base_url': 'http://www.npc.gov.cn/npc/c12435/',
            'max_pages': 2,
            'list_container_selector': '.main',
            'article_item_selector': '.clearfix.ty_list li a',
            'content_selectors': ['.article_cont', 'div[class*="content"]'],
            'date_selector': '.date',
            'source_selector': '.source',
            'page_suffix': 'list_{n}.shtml',
            'classid': 'test_traditional'
        }
        
        print("开始测试传统分页...")
        result = crawler.crawl_articles(**config)
        
        print(f"测试结果: {result}")
        print("传统分页测试完成！")
        
    except Exception as e:
        print(f"传统分页测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_article_processing():
    """测试文章批处理功能"""
    print("=" * 50)
    print("测试文章批处理功能")
    print("=" * 50)
    
    try:
        import crawler
        
        # 模拟文章列表
        test_articles = [
            ("测试文章1", "http://example.com/article1", "test_dir", "测试页面", "http://example.com", "test_class"),
            ("测试文章2", "http://example.com/article2", "test_dir", "测试页面", "http://example.com", "test_class"),
        ]
        
        print("开始测试文章批处理...")
        result = crawler.process_articles_batch(
            all_articles=test_articles,
            content_selectors=['.content', 'div[class*="article"]'],
            date_selector='.date',
            source_selector='.source',
            max_workers=2
        )
        
        print(f"批处理结果: {result}")
        print("文章批处理测试完成！")
        
    except Exception as e:
        print(f"文章批处理测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_pagination_handler():
    """测试PaginationHandler功能"""
    print("=" * 50)
    print("测试PaginationHandler功能")
    print("=" * 50)
    
    try:
        from PaginationHandler import PaginationHandler
        
        # 创建模拟的页面对象（这里只是测试导入）
        print("PaginationHandler导入成功")
        
        # 测试文章管理功能
        class MockPage:
            def __init__(self):
                self.url = "http://example.com"
        
        handler = PaginationHandler(MockPage())
        
        # 测试添加文章
        test_articles = [
            ("文章1", "http://example.com/1", "dir1", "页面1", "http://example.com", "class1"),
            ("文章2", "http://example.com/2", "dir2", "页面2", "http://example.com", "class2"),
        ]
        
        handler.add_articles(test_articles)
        print(f"添加文章后，总数: {handler.get_articles_count()}")
        
        all_articles = handler.get_all_articles()
        print(f"获取到的文章: {len(all_articles)} 篇")
        
        handler.clear_articles()
        print(f"清空后，总数: {handler.get_articles_count()}")
        
        print("PaginationHandler测试完成！")
        
    except Exception as e:
        print(f"PaginationHandler测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_crawl_with_all_articles():
    """测试使用all_articles参数的crawl_articles功能"""
    print("=" * 50)
    print("测试crawl_articles的all_articles参数")
    print("=" * 50)
    
    try:
        import crawler
        
        # 模拟预处理的文章列表
        test_articles = [
            ("预处理文章1", "http://example.com/pre1", "test_save_dir", "预处理页面", "http://example.com", "pre_class"),
            ("预处理文章2", "http://example.com/pre2", "test_save_dir", "预处理页面", "http://example.com", "pre_class"),
        ]
        
        print("开始测试all_articles参数...")
        result = crawler.crawl_articles(
            all_articles=test_articles,
            content_selectors=['.content', 'div[class*="article"]'],
            date_selector='.date',
            source_selector='.source',
            max_workers=2
        )
        
        print(f"all_articles处理结果: {result}")
        print("all_articles参数测试完成！")
        
    except Exception as e:
        print(f"all_articles参数测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试重构后的爬虫模块...")
    print()
    
    # 运行各项测试
    test_pagination_handler()
    print()
    
    test_article_processing()
    print()
    
    test_crawl_with_all_articles()
    print()
    
    # 注意：传统分页测试需要网络连接，可能会失败
    # test_traditional_pagination()
    
    print("=" * 50)
    print("所有测试完成！")
    print("=" * 50)
