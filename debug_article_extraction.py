#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试文章提取问题 - 检查上海人大网站的HTML结构
"""

import asyncio
from playwright.async_api import async_playwright
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_website_structure():
    """调试网站结构，找出正确的选择器"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 访问目标网站
            url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
            logger.info(f"访问网站: {url}")
            await page.goto(url, wait_until="networkidle")
            
            # 等待页面完全加载
            await page.wait_for_timeout(3000)
            
            # 检查页面标题
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 检查常见的容器选择器
            container_selectors = [
                "#largeData",
                ".main",
                ".content",
                ".list",
                ".news-list",
                "ul",
                ".clearfix",
                "#content"
            ]
            
            logger.info("=" * 60)
            logger.info("检查容器选择器")
            logger.info("=" * 60)
            
            for selector in container_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        logger.info(f"✅ 找到容器 '{selector}': {len(elements)} 个元素")
                        
                        # 检查第一个容器的内容
                        if elements:
                            first_element = elements[0]
                            inner_html = await first_element.inner_html()
                            text_content = await first_element.text_content()
                            logger.info(f"   第一个元素的文本长度: {len(text_content.strip()) if text_content else 0}")
                            if text_content and len(text_content.strip()) > 0:
                                logger.info(f"   文本预览: {text_content.strip()[:100]}...")
                    else:
                        logger.info(f"❌ 未找到容器 '{selector}'")
                except Exception as e:
                    logger.warning(f"❌ 检查容器 '{selector}' 时出错: {e}")
            
            # 检查文章链接选择器
            article_selectors = [
                "li a",
                "a",
                ".clearfix li a",
                ".ty_list li a",
                ".news-item a",
                "ul li a",
                "#largeData li a",
                "#largeData a"
            ]
            
            logger.info("=" * 60)
            logger.info("检查文章链接选择器")
            logger.info("=" * 60)
            
            for selector in article_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        logger.info(f"✅ 找到文章链接 '{selector}': {len(elements)} 个元素")
                        
                        # 检查前3个链接的详细信息
                        for i, element in enumerate(elements[:3]):
                            href = await element.get_attribute('href')
                            text = await element.text_content()
                            logger.info(f"   链接 {i+1}: href='{href}', text='{text.strip() if text else ''}'")
                    else:
                        logger.info(f"❌ 未找到文章链接 '{selector}'")
                except Exception as e:
                    logger.warning(f"❌ 检查文章链接 '{selector}' 时出错: {e}")
            
            # 检查加载元素
            load_selectors = [
                "#load1",
                "#load2", 
                "#load3",
                ".load-more",
                ".more-btn"
            ]
            
            logger.info("=" * 60)
            logger.info("检查加载元素")
            logger.info("=" * 60)
            
            for selector in load_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        logger.info(f"✅ 找到加载元素 '{selector}'")
                        text = await element.text_content()
                        visible = await element.is_visible()
                        logger.info(f"   文本: '{text.strip() if text else ''}'")
                        logger.info(f"   可见: {visible}")
                    else:
                        logger.info(f"❌ 未找到加载元素 '{selector}'")
                except Exception as e:
                    logger.warning(f"❌ 检查加载元素 '{selector}' 时出错: {e}")
            
            # 获取页面的完整HTML结构（截取部分）
            logger.info("=" * 60)
            logger.info("页面HTML结构分析")
            logger.info("=" * 60)
            
            # 检查body的直接子元素
            body_children = await page.evaluate("""
                () => {
                    const body = document.body;
                    const children = Array.from(body.children);
                    return children.map(child => ({
                        tagName: child.tagName,
                        id: child.id,
                        className: child.className,
                        textLength: child.textContent ? child.textContent.trim().length : 0
                    }));
                }
            """)
            
            logger.info("Body的直接子元素:")
            for child in body_children:
                logger.info(f"  {child['tagName']} id='{child['id']}' class='{child['className']}' textLength={child['textLength']}")
            
            # 查找所有包含链接的元素
            logger.info("=" * 60)
            logger.info("查找所有包含链接的容器")
            logger.info("=" * 60)
            
            containers_with_links = await page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('*');
                    const containersWithLinks = [];
                    
                    allElements.forEach(element => {
                        const links = element.querySelectorAll('a[href]');
                        if (links.length >= 5) { // 至少包含5个链接的容器
                            containersWithLinks.push({
                                tagName: element.tagName,
                                id: element.id,
                                className: element.className,
                                linkCount: links.length,
                                selector: element.id ? `#${element.id}` : (element.className ? `.${element.className.split(' ')[0]}` : element.tagName.toLowerCase())
                            });
                        }
                    });
                    
                    return containersWithLinks.slice(0, 10); // 返回前10个
                }
            """)
            
            logger.info("包含多个链接的容器:")
            for container in containers_with_links:
                logger.info(f"  {container['selector']} ({container['tagName']}) - {container['linkCount']} 个链接")
            
            # 截图保存
            await page.screenshot(path="debug_website_structure.png", full_page=True)
            logger.info("已保存页面截图: debug_website_structure.png")
            
        except Exception as e:
            logger.error(f"调试过程中出错: {e}")
        finally:
            await context.close()
            await browser.close()

async def test_extraction_with_different_selectors():
    """使用不同的选择器测试文章提取"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
            await page.goto(url, wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # 测试不同的选择器组合
            test_configs = [
                {
                    "name": "配置1: #largeData + li a",
                    "list_container_selector": "#largeData",
                    "article_item_selector": "li a"
                },
                {
                    "name": "配置2: body + a",
                    "list_container_selector": "body",
                    "article_item_selector": "a"
                },
                {
                    "name": "配置3: ul + li a",
                    "list_container_selector": "ul",
                    "article_item_selector": "li a"
                },
                {
                    "name": "配置4: .main + a",
                    "list_container_selector": ".main",
                    "article_item_selector": "a"
                }
            ]
            
            logger.info("=" * 60)
            logger.info("测试不同的选择器配置")
            logger.info("=" * 60)
            
            for config in test_configs:
                logger.info(f"\n测试 {config['name']}")
                logger.info(f"容器选择器: {config['list_container_selector']}")
                logger.info(f"文章选择器: {config['article_item_selector']}")
                
                try:
                    # 查找容器
                    containers = await page.query_selector_all(config['list_container_selector'])
                    logger.info(f"找到容器数量: {len(containers)}")
                    
                    total_articles = 0
                    for i, container in enumerate(containers):
                        # 在容器中查找文章链接
                        articles = await container.query_selector_all(config['article_item_selector'])
                        logger.info(f"  容器 {i+1} 中的文章数量: {len(articles)}")
                        total_articles += len(articles)
                        
                        # 显示前3个文章的信息
                        for j, article in enumerate(articles[:3]):
                            href = await article.get_attribute('href')
                            text = await article.text_content()
                            logger.info(f"    文章 {j+1}: '{text.strip() if text else ''}' -> {href}")
                    
                    logger.info(f"总文章数量: {total_articles}")
                    
                except Exception as e:
                    logger.error(f"测试配置时出错: {e}")
            
        except Exception as e:
            logger.error(f"测试过程中出错: {e}")
        finally:
            await context.close()
            await browser.close()

async def main():
    """主函数"""
    logger.info("开始调试上海人大网站的文章提取问题...")
    
    # 第一步：分析网站结构
    await debug_website_structure()
    
    # 第二步：测试不同的选择器配置
    await test_extraction_with_different_selectors()
    
    logger.info("调试完成！请查看日志输出和截图文件。")

if __name__ == "__main__":
    asyncio.run(main())
