list_container_selector=.lfzg_lfdt ul
article_item_selector=.lfzg_lfdt ul li

======== 简要描述 =========
这是一个政府网站的标准列表页，采用传统的静态HTML结构。主要特点：
1. 列表容器为class="lfzg_lfdt"下的ul元素
2. 每篇文章是ul下的li元素，包含标题链接和日期
3. 标题在a标签内，日期在span标签内
4. 采用固定URL分页（"更多>>"链接指向/lfdt/）
5. 无明显的翻页参数，可能是通过不同页面实现分页

======= 容器分析结果 ======
list_container_selector=.lfzg_lfdt ul
article_item_selector=.lfzg_lfdt ul li

建议采集配置：
1. 基础URL：http://www.zhrd.gov.cn/lfgz/lfdt/
2. 内容提取规则：
   - 标题：li > a的text和title属性
   - 链接：li > a的href属性（需补全域名）
   - 日期：li > span的text
3. 翻页规则：跟踪"更多>>"链接并递归采集
4. 注意：部分标题有截断，建议优先使用title属性获取完整标题