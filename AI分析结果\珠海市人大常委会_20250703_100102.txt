======== 简要描述 =========
该页面是一个政府网站的文章列表页，结构清晰，采用传统的<ul><li>列表展示文章条目。特点包括：
1. 分页明确，总页数10页，通过index_N.html形式翻页
2. 每篇文章包含日期和标题链接
3. 文章条目在<div class="n_new_list">容器下的<ul>中

======= 容器分析结果 ======
list_container_selector=.n_new_list > ul
article_item_selector=.n_new_list > ul > li

======== 采集配置建议 ========
1. 基础URL：http://www.zhrd.gov.cn/lfgz/lfdt/
2. 翻页规则：index_[1-9].html（从1到9）
3. 字段提取：
   - 标题：li > a的text内容
   - 链接：li > a的href属性（需补全域名）
   - 日期：li > span的text内容
4. 置信度：高（结构清晰稳定）
5. 注意事项：
   - 部分链接可能需要补全相对路径
   - 日期格式统一为YYYY-MM-DD
   - 可考虑过滤掉重复日期条目