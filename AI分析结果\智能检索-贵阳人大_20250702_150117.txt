list_container_selector=.js_basic_result_content
article_item_selector=.item.hasImg.is-news

======== 简要描述 =========
这是一个政府网站的搜索结果显示页面，采用典型的分块式布局。主要特点：
1. 搜索结果以列表形式展示在左侧内容区
2. 每篇文章包含标题、描述、来源和时间等信息
3. 采用常规的分页方式，底部有页码导航
4. 翻页通过JavaScript控制，URL不变（POST请求）
5. 文章容器结构清晰，包含完整元数据

======= 容器分析结果 ======
list_container_selector=.js_basic_result_content  // 主列表容器，包含所有文章项
article_item_selector=.item.hasImg.is-news  // 每篇文章的容器，包含标题、描述等完整元素

解释说明：
1. 主列表容器选择器定位到包含所有搜索结果的最外层div
2. 文章项选择器精确匹配包含图片的新闻类条目（排除右侧推荐等干扰项）
3. 该配置可完整提取标题、描述、发布时间和来源等关键字段
4. 翻页需要模拟点击"下一页"按钮或直接构造分页参数（观察URL无规律变化）

建议采集字段：
- 标题：.title
- 链接：.title元素的href属性 
- 摘要：.description .js_text
- 发布时间：.sourceTime
- 来源：.source