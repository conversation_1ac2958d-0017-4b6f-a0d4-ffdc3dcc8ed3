#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI配置生成
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QComboBox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_combo_box():
    """测试QComboBox的行为"""
    print("=" * 60)
    print("测试QComboBox的行为")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # 创建一个测试用的ComboBox
    combo = QComboBox()
    combo.addItems([
        "禁用动态翻页",
        "点击翻页", 
        "滚动翻页",
        "iframe翻页"
    ])
    
    print("初始状态:")
    print(f"  currentText(): '{combo.currentText()}'")
    print(f"  currentIndex(): {combo.currentIndex()}")
    
    # 设置为滚动翻页
    combo.setCurrentText("滚动翻页")
    print("\n设置为'滚动翻页'后:")
    print(f"  currentText(): '{combo.currentText()}'")
    print(f"  currentIndex(): {combo.currentIndex()}")
    
    # 测试配置生成逻辑
    pagination_type = combo.currentText()
    config = {
        'pagination_type': pagination_type,
        'enabled': pagination_type != "禁用动态翻页"
    }
    
    print(f"\n生成的配置:")
    print(f"  pagination_type: '{config['pagination_type']}'")
    print(f"  enabled: {config['enabled']}")
    
    # 测试条件判断
    enabled = config.get('enabled', False)
    type_check = config.get('pagination_type') != '禁用动态翻页'
    
    print(f"\n条件判断:")
    print(f"  enabled: {enabled}")
    print(f"  pagination_type != '禁用动态翻页': {type_check}")
    print(f"  最终条件: {enabled and type_check}")
    
    if enabled and type_check:
        print("  ✅ 应该使用动态翻页")
    else:
        print("  ❌ 将使用传统翻页")
    
    app.quit()

def simulate_gui_config():
    """模拟GUI配置生成过程"""
    print("\n" + "=" * 60)
    print("模拟GUI配置生成过程")
    print("=" * 60)
    
    # 模拟用户在GUI中的操作
    class MockGUI:
        def __init__(self):
            # 模拟GUI组件
            self.pagination_type_combo = QComboBox()
            self.pagination_type_combo.addItems([
                "禁用动态翻页",
                "点击翻页",
                "滚动翻页", 
                "iframe翻页"
            ])
            
            # 模拟滚动翻页的其他组件
            self.scroll_container_selector_edit = MockLineEdit('#largeData')
            self.scroll_step_spin = MockSpinBox(500)
            self.scroll_delay_spin = MockSpinBox(1000)
            self.max_scrolls_spin = MockSpinBox(20)
            self.load_indicator_selector_edit = MockLineEdit('')
            self.scroll_timeout_spin = MockSpinBox(10000)
            self.height_tolerance_spin = MockSpinBox(50)
            self.load_element_pattern_edit = MockLineEdit('#load{n}')
        
        def get_pagination_config(self):
            """获取翻页配置"""
            pagination_type = self.pagination_type_combo.currentText()
            
            config = {
                'pagination_type': pagination_type,
                'enabled': pagination_type != "禁用动态翻页"
            }
            
            if pagination_type == "滚动翻页":
                config.update({
                    'scroll_container_selector': self.scroll_container_selector_edit.text().strip(),
                    'scroll_step': self.scroll_step_spin.value(),
                    'scroll_delay': self.scroll_delay_spin.value(),
                    'max_scrolls': self.max_scrolls_spin.value(),
                    'load_indicator_selector': self.load_indicator_selector_edit.text().strip(),
                    'scroll_timeout': self.scroll_timeout_spin.value(),
                    'height_tolerance': self.height_tolerance_spin.value(),
                    'load_element_pattern': self.load_element_pattern_edit.text().strip()
                })
            
            return config
        
        def get_current_config(self):
            """获取当前配置"""
            return {
                "input_url": "https://www.shrd.gov.cn/n8347/n8378/index.html",
                "base_url": "https://www.shrd.gov.cn",
                "max_pages": "5",
                "list_container_selector": "#largeData",
                "article_item_selector": "li a",
                "pagination_config": self.get_pagination_config()
            }
    
    class MockLineEdit:
        def __init__(self, text):
            self._text = text
        def text(self):
            return self._text
    
    class MockSpinBox:
        def __init__(self, value):
            self._value = value
        def value(self):
            return self._value
    
    app = QApplication(sys.argv)
    
    # 创建模拟GUI
    gui = MockGUI()
    
    # 测试默认状态（禁用动态翻页）
    print("默认状态（禁用动态翻页）:")
    config = gui.get_pagination_config()
    print(f"  配置: {config}")
    
    # 设置为滚动翻页
    gui.pagination_type_combo.setCurrentText("滚动翻页")
    print("\n设置为滚动翻页后:")
    config = gui.get_pagination_config()
    print(f"  配置:")
    for key, value in config.items():
        print(f"    {key}: {value}")
    
    # 测试完整配置
    print("\n完整配置:")
    full_config = gui.get_current_config()
    print(f"  pagination_config: {full_config['pagination_config']}")
    
    # 测试条件判断
    pagination_config = full_config['pagination_config']
    enabled = pagination_config.get('enabled', False)
    pagination_type = pagination_config.get('pagination_type')
    
    print(f"\n条件判断:")
    print(f"  enabled: {enabled}")
    print(f"  pagination_type: '{pagination_type}'")
    print(f"  pagination_type != '禁用动态翻页': {pagination_type != '禁用动态翻页'}")
    
    if enabled and pagination_type != '禁用动态翻页':
        print("  ✅ 应该使用动态翻页")
    else:
        print("  ❌ 将使用传统翻页")
    
    app.quit()

def main():
    """主测试函数"""
    print("开始测试GUI配置生成...")
    
    try:
        test_combo_box()
        simulate_gui_config()
        
        print("\n" + "=" * 60)
        print("测试结论")
        print("=" * 60)
        print("GUI组件和配置生成逻辑都是正确的。")
        print("问题可能出现在:")
        print("1. 用户实际选择的不是'滚动翻页'")
        print("2. GUI组件没有正确初始化")
        print("3. 配置传递过程中出现问题")
        print("4. 需要查看实际运行时的调试输出")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("这可能是因为没有显示环境，但逻辑测试应该是有效的")

if __name__ == "__main__":
    main()
