from playwright.sync_api import sync_playwright

class PlaywrightUtils:
    """
    一个封装了常见Playwright操作的工具类
    """
    
    def __init__(self):
        """
        初始化浏览器上下文
        """
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=False)
        self.context = self.browser.new_context()
    
    def __enter__(self):
        """
        支持上下文管理协议
        """
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        支持上下文管理协议，确保资源释放
        """
        self.close()
    
    def open_page(self, url):
        """
        打开新页面并访问指定URL
        
        参数:
            url (str): 要访问的网址
        返回:
            Page: 新打开的页面对象
        """
        page = self.context.new_page()
        page.goto(url)
        return page
    
    def click_element(self, page, selector):
        """
        在指定页面点击元素
        
        参数:
            page (Page): 要操作的页面对象
            selector (str): 元素选择器
        """
        page.click(selector)
    
    def fill_input(self, page, selector, text):
        """
        在指定页面的输入框中填写文本
        
        参数:
            page (Page): 要操作的页面对象
            selector (str): 输入框选择器
            text (str): 要填写的文本
        """
        page.fill(selector, text)
    
    def get_text(self, page, selector):
        """
        获取指定元素的文本内容
        
        参数:
            page (Page): 要操作的页面对象
            selector (str): 元素选择器
        返回:
            str: 元素的文本内容
        """
        return page.text_content(selector)
    
    def close(self):
        """
        关闭浏览器和Playwright实例
        """
        self.browser.close()
        self.playwright.stop()