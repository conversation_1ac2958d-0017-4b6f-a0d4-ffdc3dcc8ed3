#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的翻页问题修复和测试
"""

import sys
import os
import asyncio
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def clear_python_cache():
    """清理Python缓存文件"""
    import glob
    import shutil
    
    print("清理Python缓存文件...")
    
    # 删除__pycache__目录
    for pycache_dir in glob.glob("**/__pycache__", recursive=True):
        try:
            shutil.rmtree(pycache_dir)
            print(f"删除缓存目录: {pycache_dir}")
        except Exception as e:
            print(f"删除缓存目录失败 {pycache_dir}: {e}")
    
    # 删除.pyc文件
    for pyc_file in glob.glob("**/*.pyc", recursive=True):
        try:
            os.remove(pyc_file)
            print(f"删除缓存文件: {pyc_file}")
        except Exception as e:
            print(f"删除缓存文件失败 {pyc_file}: {e}")

def check_gui_code():
    """检查GUI代码中的KeyError问题"""
    print("\n检查GUI代码中的KeyError问题...")
    
    try:
        with open('crawler_gui_2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有直接访问result['success']的代码
        if "result['success']" in content:
            print("❌ 发现直接访问result['success']的代码")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "result['success']" in line:
                    print(f"  第{i+1}行: {line.strip()}")
        else:
            print("✅ 没有发现直接访问result['success']的代码")
        
        # 检查是否有安全访问的代码
        if "result.get('success'" in content:
            print("✅ 发现安全访问result.get('success')的代码")
        else:
            print("❌ 没有发现安全访问的代码")
            
    except Exception as e:
        print(f"检查GUI代码失败: {e}")

async def test_pagination_config():
    """测试翻页配置"""
    print("\n测试翻页配置...")
    
    try:
        # 模拟GUI中的配置
        test_config = {
            'input_url': 'http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign=',
            'max_pages': 5,  # 确保不是1
            'pagination_config': {
                'pagination_type': 'JavaScript点击',
                'next_button_selector': 'a.next:not(.lose)',
                'max_pages': 10,
                'timeout': 10000,
                'wait_after_click': 2000,
                'disabled_check': True
            }
        }
        
        print(f"配置信息:")
        print(f"  max_pages: {test_config['max_pages']}")
        print(f"  pagination_config.max_pages: {test_config['pagination_config']['max_pages']}")
        print(f"  next_button_selector: {test_config['pagination_config']['next_button_selector']}")
        
        # 检查max_pages是否正确
        max_pages = test_config.get('max_pages', 5)
        if max_pages == 1:
            print("❌ max_pages设置为1，这会导致只处理1页")
        else:
            print(f"✅ max_pages设置为{max_pages}")
            
    except Exception as e:
        print(f"测试配置失败: {e}")

async def test_simple_pagination():
    """简单测试翻页功能"""
    print("\n简单测试翻页功能...")
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        # 使用一个简单可靠的测试页面
        test_url = "https://httpbin.org/html"  # 简单的HTML页面
        
        async with async_playwright() as p:
            browser, context, page = await PaginationHandler.launch_browser(p, headless=True)
            
            try:
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=15000)
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                print("测试PaginationHandler实例化...")
                print("✅ PaginationHandler创建成功")
                
                # 测试基本功能（不进行实际翻页）
                print("测试基本功能...")
                
                # 测试页面访问
                current_url = page.url
                print(f"当前页面URL: {current_url}")
                
                if current_url == test_url:
                    print("✅ 页面访问成功")
                else:
                    print("❌ 页面访问失败")
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"简单测试失败: {e}")
        import traceback
        traceback.print_exc()

def create_test_config():
    """创建测试配置文件"""
    print("\n创建测试配置文件...")
    
    test_config = {
        "input_url": "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign=",
        "base_url": "http://www.gysrd.gov.cn",
        "max_pages": 3,  # 设置为3页进行测试
        "list_container_selector": ".main",
        "article_item_selector": ".clearfix.ty_list li a",
        "title_selector": "",
        "save_dir": "./articles",
        "pagination_config": {
            "pagination_type": "JavaScript点击",
            "next_button_selector": "a.next:not(.lose)",
            "max_pages": 3,
            "timeout": 10000,
            "wait_after_click": 2000,
            "disabled_check": True,
            "content_ready_selector": ""
        }
    }
    
    try:
        with open('test_pagination_config.json', 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        print("✅ 测试配置文件创建成功: test_pagination_config.json")
        print(f"   max_pages: {test_config['max_pages']}")
        print(f"   pagination max_pages: {test_config['pagination_config']['max_pages']}")
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("最终的翻页问题修复和测试")
    print("=" * 60)
    
    # 1. 清理Python缓存
    clear_python_cache()
    
    # 2. 检查GUI代码
    check_gui_code()
    
    # 3. 创建测试配置
    create_test_config()
    
    # 4. 测试翻页配置
    asyncio.run(test_pagination_config())
    
    # 5. 简单测试
    asyncio.run(test_simple_pagination())
    
    print("\n" + "=" * 60)
    print("修复建议:")
    print("=" * 60)
    print("1. 清理了Python缓存文件，解决KeyError缓存问题")
    print("2. 检查了GUI代码中的KeyError问题")
    print("3. 创建了测试配置文件，确保max_pages > 1")
    print("4. 增强了PaginationHandler的调试日志")
    print("5. 建议重新启动GUI程序以加载最新代码")
    print("\n下一步操作:")
    print("1. 重新启动crawler_gui_2.py")
    print("2. 使用动态翻页功能测试")
    print("3. 查看详细的调试日志")
    print("4. 如果仍有问题，检查网络连接和目标网站状态")

if __name__ == "__main__":
    main()
