import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel, QLineEdit, 
                            QPushButton, QTextEdit, QVBoxLayout, QHBoxLayout, 
                            QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                            QComboBox, QInputDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import crawler
import myconfig

class CrawlerThread(QThread):
    # 定义信号
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总数
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
    def run(self):
        # 将日志信号传递给爬虫
        self.config['log_callback'] = self.log_signal.emit
        # 运行爬虫
        result = crawler.crawl_articles(**self.config)
        self.finished_signal.emit(result)

class CrawlerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Hillsun's文章采集器v0.1")
        self.setGeometry(100, 100, 800, 750)  # 增加高度以适应新控件
        
        # 初始化配置管理器
        self.config_manager = myconfig.ConfigManager()
        
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        self.main_layout = QVBoxLayout()
        self.central_widget.setLayout(self.main_layout)
        
        self.create_config_group()  # 添加配置组管理
        self.create_url_group()
        self.create_selector_group()
        self.create_progress_bar()
        self.create_control_buttons()
        self.create_log_area()
        
        # 添加作者标签
        self.author_label = QLabel('作者：Hillsun <EMAIL>')
        self.author_label.setAlignment(Qt.AlignRight)
        self.author_label.setStyleSheet("color: gray; font-size: 12px;")
        self.main_layout.addWidget(self.author_label)
        
        # 加载配置
        self.load_config()

        self.crawler_thread = None
        
    def create_config_group(self):
        """创建配置组管理区域"""
        group = QGroupBox("配置管理")
        layout = QHBoxLayout()
        
        # 配置组选择
        self.config_label = QLabel("配置组:")
        self.config_combo = QComboBox()
        self.config_combo.currentIndexChanged.connect(self.config_group_changed)
        
        # 配置管理按钮
        self.save_config_button = QPushButton("保存配置")
        self.save_config_button.clicked.connect(self.save_config)
        self.save_config_button.setToolTip("保存当前配置到选中的配置组")
        
        self.save_as_button = QPushButton("另存为")
        self.save_as_button.clicked.connect(self.save_config_as)
        self.save_as_button.setToolTip("创建新的配置组")
        
        self.delete_config_button = QPushButton("删除配置")
        self.delete_config_button.clicked.connect(self.delete_config)
        self.delete_config_button.setToolTip("删除选中的配置组")
        
        # 添加到布局
        layout.addWidget(self.config_label)
        layout.addWidget(self.config_combo)
        layout.addWidget(self.save_config_button)
        layout.addWidget(self.save_as_button)
        layout.addWidget(self.delete_config_button)
        layout.addStretch()
        
        group.setLayout(layout)
        self.main_layout.addWidget(group)
    
    def create_url_group(self):
        group = QGroupBox("URL 设置")
        grid = QGridLayout()
        # 输入URL
        self.input_url_label = QLabel("输入URL:")
        self.input_url_edit = QLineEdit()
        grid.addWidget(self.input_url_label, 0, 0)
        grid.addWidget(self.input_url_edit, 0, 1, 1, 2)
        # 基础URL
        self.base_url_label = QLabel("基础URL:")
        self.base_url_edit = QLineEdit()
        grid.addWidget(self.base_url_label, 1, 0)
        grid.addWidget(self.base_url_edit, 1, 1, 1, 2)
        # 最大页数、翻页后缀、翻页起始数字同一行
        self.max_pages_label = QLabel("最大页数:")
        self.max_pages_edit = QLineEdit()
        self.max_pages_edit.setMaximumWidth(80)
        self.page_suffix_label = QLabel("翻页后缀:")
        self.page_suffix_edit = QLineEdit()
        self.page_suffix_edit.setPlaceholderText("如 _{n}.html 或 index_{n}.html")
        self.page_suffix_edit.setMaximumWidth(120)
        self.page_suffix_start_label = QLabel("翻页起始数字:")
        self.page_suffix_start_edit = QLineEdit()
        self.page_suffix_start_edit.setPlaceholderText("如 1 或 2")
        self.page_suffix_start_edit.setMaximumWidth(60)
        hbox = QHBoxLayout()
        hbox.addWidget(self.max_pages_label)
        hbox.addWidget(self.max_pages_edit)
        hbox.addStretch(1)
        hbox.addWidget(self.page_suffix_label)
        hbox.addWidget(self.page_suffix_edit)
        hbox.addStretch(1)
        hbox.addWidget(self.page_suffix_start_label)
        hbox.addWidget(self.page_suffix_start_edit)
        grid.addLayout(hbox, 2, 0, 1, 3)
        # 正文URL规则
        self.url_mode_label = QLabel("正文URL规则:")
        self.url_mode_combo = QComboBox()
        self.url_mode_combo.addItems(["绝对路径", "相对路径(base_url+href)"])
        grid.addWidget(self.url_mode_label, 3, 0)
        grid.addWidget(self.url_mode_combo, 3, 1)
        # 浏览器选择
        self.browser_label = QLabel("浏览器:")
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["firefox", "chrome", "edge"])
        grid.addWidget(self.browser_label, 4, 0)
        grid.addWidget(self.browser_combo, 4, 1)
        # 新增：无头模式选择
        self.headless_label = QLabel("无头模式:")
        self.headless_combo = QComboBox()
        self.headless_combo.addItems(["是", "否"])
        grid.addWidget(self.headless_label, 5, 0)
        grid.addWidget(self.headless_combo, 5, 1)
        # 新增：窗口尺寸
        self.window_size_label = QLabel("窗口尺寸:")
        self.window_size_edit = QLineEdit()
        self.window_size_edit.setPlaceholderText("如 1200,800")
        grid.addWidget(self.window_size_label, 6, 0)
        grid.addWidget(self.window_size_edit, 6, 1)
        # 新增：页面加载策略
        self.page_load_strategy_label = QLabel("页面加载策略:")
        self.page_load_strategy_combo = QComboBox()
        self.page_load_strategy_combo.addItems(["normal", "eager", "none"])
        grid.addWidget(self.page_load_strategy_label, 7, 0)
        grid.addWidget(self.page_load_strategy_combo, 7, 1)
        group.setLayout(grid)
        self.main_layout.addWidget(group)
    
    def create_selector_group(self):
        group = QGroupBox("选择器设置")
        grid = QGridLayout()
        # 列表容器选择器
        self.list_container_label = QLabel("列表容器选择器:")
        self.list_container_edit = QLineEdit()
        self.list_container_type = QComboBox()
        self.list_container_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.list_container_label, 0, 0)
        grid.addWidget(self.list_container_edit, 0, 1)
        grid.addWidget(self.list_container_type, 0, 2)
        # 文章项选择器
        self.article_item_label = QLabel("文章项选择器:")
        self.article_item_edit = QLineEdit()
        self.article_item_type = QComboBox()
        self.article_item_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.article_item_label, 1, 0)
        grid.addWidget(self.article_item_edit, 1, 1)
        grid.addWidget(self.article_item_type, 1, 2)
        # 文章标题选择器
        self.title_selector_label = QLabel("文章标题选择器:")
        self.title_selector_edit = QLineEdit()
        self.title_selector_type = QComboBox()
        self.title_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.title_selector_label, 2, 0)
        grid.addWidget(self.title_selector_edit, 2, 1)
        grid.addWidget(self.title_selector_type, 2, 2)
        # 正文内容选择器
        self.content_label = QLabel("正文内容选择器(多个用;分隔):")
        self.content_edit = QLineEdit()
        self.content_type = QComboBox()
        self.content_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.content_label, 3, 0)
        grid.addWidget(self.content_edit, 3, 1)
        grid.addWidget(self.content_type, 3, 2)
        # 日期选择器
        self.date_selector_label = QLabel("日期选择器:")
        self.date_selector_edit = QLineEdit()
        self.date_selector_type = QComboBox()
        self.date_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.date_selector_label, 4, 0)
        grid.addWidget(self.date_selector_edit, 4, 1)
        grid.addWidget(self.date_selector_type, 4, 2)
        # 来源选择器
        self.source_selector_label = QLabel("来源选择器:")
        self.source_selector_edit = QLineEdit()
        self.source_selector_type = QComboBox()
        self.source_selector_type.addItems(["CSS", "XPath"])
        grid.addWidget(self.source_selector_label, 5, 0)
        grid.addWidget(self.source_selector_edit, 5, 1)
        grid.addWidget(self.source_selector_type, 5, 2)
        group.setLayout(grid)
        self.main_layout.addWidget(group)
    
    def create_progress_bar(self):
        # 进度条
        self.progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        
        self.main_layout.addLayout(progress_layout)
    
    def create_control_buttons(self):
        hbox = QHBoxLayout()
        
        self.run_button = QPushButton("开始爬取")
        self.run_button.clicked.connect(self.start_crawler)
        self.run_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        
        self.stop_button = QPushButton("停止爬取")
        self.stop_button.clicked.connect(self.stop_crawler)
        self.stop_button.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        self.stop_button.setEnabled(False)
        
        self.clear_button = QPushButton("清空日志")
        self.clear_button.clicked.connect(self.clear_log)
        self.clear_button.setStyleSheet("background-color: #2196F3; color: white;")
        
        hbox.addWidget(self.run_button)
        hbox.addWidget(self.stop_button)
        hbox.addWidget(self.clear_button)
        
        self.main_layout.addLayout(hbox)
    
    def create_log_area(self):
        group = QGroupBox("运行日志")
        layout = QVBoxLayout()
        
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)
        
        group.setLayout(layout)
        self.main_layout.addWidget(group)
    
    def load_config(self):
        """加载配置到界面"""
        # 更新配置组下拉框
        self.config_combo.clear()
        groups = self.config_manager.get_groups()
        self.config_combo.addItems(groups)
        
        # 设置当前选中的配置组
        current_group = self.config_manager.get_current_group()
        if current_group in groups:
            index = groups.index(current_group)
            self.config_combo.setCurrentIndex(index)
        else:
            self.config_combo.setCurrentIndex(0)
        
        # 加载当前配置组的数据
        self.config_group_changed()
    
    def config_group_changed(self):
        """当配置组改变时，加载对应的配置"""
        group_name = self.config_combo.currentText()
        group_config = self.config_manager.get_group(group_name)
        
        if group_config:
            # 更新UI控件
            self.input_url_edit.setText(group_config.get("input_url", ""))
            self.base_url_edit.setText(group_config.get("base_url", ""))
            self.max_pages_edit.setText(str(group_config.get("max_pages", "3")))
            self.list_container_edit.setText(group_config.get("list_container_selector", ""))
            self.list_container_type.setCurrentIndex(0 if group_config.get("list_container_type", "CSS") == "CSS" else 1)
            self.article_item_edit.setText(group_config.get("article_item_selector", ""))
            self.article_item_type.setCurrentIndex(0 if group_config.get("article_item_type", "CSS") == "CSS" else 1)
            self.title_selector_edit.setText(group_config.get("title_selector", ""))
            self.title_selector_type.setCurrentIndex(0 if group_config.get("title_selector_type", "CSS") == "CSS" else 1)
            self.content_edit.setText(group_config.get("content_selectors", ""))
            self.content_type.setCurrentIndex(0 if group_config.get("content_type", "CSS") == "CSS" else 1)
            self.date_selector_edit.setText(group_config.get("date_selector", ""))
            self.date_selector_type.setCurrentIndex(0 if group_config.get("date_selector_type", "CSS") == "CSS" else 1)
            self.source_selector_edit.setText(group_config.get("source_selector", ""))
            self.source_selector_type.setCurrentIndex(0 if group_config.get("source_selector_type", "CSS") == "CSS" else 1)
            self.page_suffix_edit.setText(group_config.get("page_suffix", "index_{n}.html"))
            self.page_suffix_start_edit.setText(str(group_config.get("page_suffix_start", "1")))
            self.url_mode_combo.setCurrentIndex(
                {"absolute": 0, "relative": 1}.get(group_config.get("url_mode", "absolute"), 0)
            )
            self.page_load_strategy_combo.setCurrentIndex(
                {"normal": 0, "eager": 1, "none": 2}.get(group_config.get("page_load_strategy", "normal"), 0)
            )
            # 设置当前配置组
            self.config_manager.set_current_group(group_name)
    
    def save_config(self):
        """保存当前配置到选中的配置组"""
        group_name = self.config_combo.currentText()
        config_data = self.get_current_config()
        
        self.config_manager.add_group(group_name, config_data)
        self.log_message(f"配置 '{group_name}' 已保存")
        QMessageBox.information(self, "保存成功", f"配置已保存到 '{group_name}'")
    
    def save_config_as(self):
        """将当前配置保存为新的配置组"""
        # 获取新配置组名称
        new_name, ok = QInputDialog.getText(
            self, 
            "新建配置组", 
            "请输入配置组名称:", 
            QLineEdit.Normal
        )
        
        if ok and new_name:
            # 检查名称是否已存在
            groups = self.config_manager.get_groups()
            if new_name in groups:
                reply = QMessageBox.question(
                    self, 
                    "确认覆盖", 
                    f"配置组 '{new_name}' 已存在，是否覆盖?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # 获取当前配置
            config_data = self.get_current_config()
            
            # 保存新配置组
            self.config_manager.add_group(new_name, config_data)
            
            # 更新下拉框
            self.config_combo.clear()
            self.config_combo.addItems(self.config_manager.get_groups())
            self.config_combo.setCurrentText(new_name)
            
            self.log_message(f"新配置 '{new_name}' 已保存")
            QMessageBox.information(self, "保存成功", f"新配置 '{new_name}' 已创建")
    
    def delete_config(self):
        """删除选中的配置组"""
        group_name = self.config_combo.currentText()
        
        # 不能删除默认配置
        if group_name == "default":
            QMessageBox.warning(self, "操作禁止", "不能删除默认配置组")
            return
            
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除配置组 '{group_name}' 吗?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.config_manager.delete_group(group_name):
                # 更新下拉框
                groups = self.config_manager.get_groups()
                self.config_combo.clear()
                self.config_combo.addItems(groups)
                
                # 设置当前配置组
                if groups:
                    self.config_combo.setCurrentIndex(0)
                
                self.log_message(f"配置组 '{group_name}' 已删除")
            else:
                QMessageBox.warning(self, "删除失败", f"配置组 '{group_name}' 删除失败")
    
    def get_current_config(self):
        url_mode_map = {0: "absolute", 1: "relative"}
        return {
            "input_url": self.input_url_edit.text().strip(),
            "base_url": self.base_url_edit.text().strip(),
            "max_pages": self.max_pages_edit.text().strip(),
            "list_container_selector": self.list_container_edit.text().strip(),
            "list_container_type": self.list_container_type.currentText(),
            "article_item_selector": self.article_item_edit.text().strip(),
            "article_item_type": self.article_item_type.currentText(),
            "title_selector": self.title_selector_edit.text().strip(),
            "title_selector_type": self.title_selector_type.currentText(),
            "content_selectors": self.content_edit.text().strip(),
            "content_type": self.content_type.currentText(),
            "date_selector": self.date_selector_edit.text().strip(),
            "date_selector_type": self.date_selector_type.currentText(),
            "source_selector": self.source_selector_edit.text().strip(),
            "source_selector_type": self.source_selector_type.currentText(),
            "page_suffix": self.page_suffix_edit.text().strip() or "index_{n}.html",
            "page_suffix_start": int(self.page_suffix_start_edit.text().strip() or 1),
            "url_mode": url_mode_map.get(self.url_mode_combo.currentIndex(), "absolute"),
            "browser": self.browser_combo.currentText(),
            "headless": self.headless_combo.currentIndex() == 0,  # 是为True
            "window_size": self.window_size_edit.text().strip(),
            "page_load_strategy": self.page_load_strategy_combo.currentText(),
        }
    
    def start_crawler(self):
        config_data = self.get_current_config()
        diver = {"headless": config_data["headless"]}
        if config_data["window_size"]:
            diver["window_size"] = config_data["window_size"]
        if config_data["page_load_strategy"]:
            diver["page_load_strategy"] = config_data["page_load_strategy"]
        crawler_config = {
            'input_url': config_data['input_url'],
            'base_url': config_data['base_url'],
            'max_pages': int(config_data['max_pages']) if config_data['max_pages'] else None,
            'list_container_selector': config_data['list_container_selector'],
            'list_container_type': config_data['list_container_type'],
            'article_item_selector': config_data['article_item_selector'],
            'article_item_type': config_data['article_item_type'],
            'title_selector': config_data['title_selector'],
            'title_selector_type': config_data['title_selector_type'],
            'content_selectors': [s.strip() for s in config_data['content_selectors'].split(';') if s.strip()],
            'content_type': config_data['content_type'],
            'date_selector': config_data['date_selector'],
            'date_selector_type': config_data['date_selector_type'],
            'source_selector': config_data['source_selector'],
            'source_selector_type': config_data['source_selector_type'],
            'page_suffix': config_data['page_suffix'],
            'page_suffix_start': config_data['page_suffix_start'],
            'url_mode': config_data['url_mode'],
            'browser': config_data['browser'],
            'diver': diver
        }
        # 验证输入
        if not crawler_config['input_url']:
            QMessageBox.warning(self, "输入错误", "请输入有效的URL")
            return
            
        if not crawler_config['content_selectors']:
            QMessageBox.warning(self, "输入错误", "请输入正文内容选择器")
            return
            
        # 更新按钮状态
        self.run_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        
        # 记录开始信息
        self.log_message("="*50)
        self.log_message("开始爬取任务...")
        self.log_message(f"目标URL: {crawler_config['input_url']}")
        self.log_message(f"最大页数: {crawler_config['max_pages'] or '无限制'}")
        
        # 创建并启动爬虫线程
        self.crawler_thread = CrawlerThread(crawler_config)
        self.crawler_thread.log_signal.connect(self.log_message)
        self.crawler_thread.finished_signal.connect(self.crawler_finished)
        self.crawler_thread.start()
    
    def stop_crawler(self):
        if self.crawler_thread and self.crawler_thread.isRunning():
            self.log_message("用户请求停止爬取...")
            self.crawler_thread.terminate()
            self.crawler_thread.wait()
            self.log_message("爬取任务已终止")
            self.run_button.setEnabled(True)
            self.stop_button.setEnabled(False)
    
    def crawler_finished(self, result):
        self.log_message("="*50)
        self.log_message(f"爬取任务完成! 共处理 {result['total']} 篇文章")
        self.log_message(f"成功: {result['success']} 篇")
        self.log_message(f"失败: {result['failed']} 篇")
        self.log_message("="*50)
        
        self.progress_bar.setValue(100)
        self.run_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 显示完成提示
        QMessageBox.information(self, "完成", f"爬取任务完成!\n成功: {result['success']}篇\n失败: {result['failed']}篇")
    
    def clear_log(self):
        self.log_area.clear()
    
    def log_message(self, message):
        """将消息添加到日志区域"""
        self.log_area.append(message)
        self.log_area.ensureCursorVisible()  # 自动滚动到底部
        
        # 更新进度条（如果消息包含进度信息）
        if "正在处理第" in message and "篇文章" in message:
            parts = message.split(":")
            if len(parts) > 0:
                progress_part = parts[0].split("第")[1].split("/")[0]
                total_part = parts[0].split("/")[1].split("篇")[0]
                try:
                    current = int(progress_part.strip())
                    total = int(total_part.strip())
                    progress = int((current / total) * 100) if total > 0 else 0
                    self.progress_bar.setValue(progress)
                except:
                    pass

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CrawlerGUI()
    window.show()
    sys.exit(app.exec_())