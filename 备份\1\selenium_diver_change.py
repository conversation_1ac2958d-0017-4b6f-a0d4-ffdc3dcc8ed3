from selenium import webdriver
import shutil


def get_driver(browser="firefox", diver=None, driver_path=None, options_dict=None):
    """
    根据浏览器类型返回 selenium driver 实例。
    :param browser: 浏览器类型，支持 "firefox" | "chrome" | "edge"
    :param diver: dict, 额外参数，如 {"headless": True, "window_size": "1200,800", "page_load_strategy": "eager"}
    :param driver_path: 指定driver可执行文件路径（可选）
    :param options_dict: 额外的浏览器参数字典（可选）
    :return: selenium.webdriver 实例
    """
    browser = browser.lower()
    driver = None
    options = None
    diver = diver or {}
    headless = diver.get("headless", True)
    window_size = diver.get("window_size")  # 例如 "1200,800"
    page_load_strategy = diver.get("page_load_strategy", "normal")  # 新增

    if browser == "firefox":
        from selenium.webdriver.firefox.options import Options
        from selenium.webdriver.firefox.service import Service

        options = Options()
        # 设置页面加载策略
        options.page_load_strategy = page_load_strategy
        if headless:
            options.add_argument("--headless")
        if window_size:
            options.add_argument(f"--width={window_size.split(',')[0]}")
            options.add_argument(f"--height={window_size.split(',')[1]}")
        if options_dict:
            for k, v in options_dict.items():
                options.set_preference(k, v)
        driver_exec = driver_path or shutil.which("geckodriver")
        if not driver_exec:
            raise RuntimeError("未找到 Firefox geckodriver，请检查环境变量或指定 driver_path")
        service = Service(driver_exec)
        driver = webdriver.Firefox(options=options, service=service)
        driver.set_page_load_timeout(30)  # 设置页面加载超时为30秒
    elif browser == "chrome":
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service

        options = Options()
        # 设置页面加载策略
        options.page_load_strategy = page_load_strategy
        if headless:
            options.add_argument("--headless=new")
        if window_size:
            options.add_argument(f"--window-size={window_size}")
        if options_dict:
            for k, v in options_dict.items():
                options.add_argument(f"--{k}={v}")
        driver_exec = driver_path or shutil.which("chromedriver")
        if not driver_exec:
            raise RuntimeError("未找到 Chrome chromedriver，请检查环境变量或指定 driver_path")
        service = Service(driver_exec)
        driver = webdriver.Chrome(options=options, service=service)
        driver.set_page_load_timeout(30)  # 设置页面加载超时为30秒
    elif browser == "edge":
        from selenium.webdriver.edge.options import Options
        from selenium.webdriver.edge.service import Service

        options = Options()
        # 设置页面加载策略
        options.page_load_strategy = page_load_strategy
        if headless:
            options.add_argument("--headless")
        if window_size:
            options.add_argument(f"--window-size={window_size}")
        if options_dict:
            for k, v in options_dict.items():
                options.add_argument(f"--{k}={v}")
        driver_exec = driver_path or shutil.which("msedgedriver")
        if not driver_exec:
            raise RuntimeError("未找到 Edge msedgedriver，请检查环境变量或指定 driver_path")
        service = Service(driver_exec)
        driver = webdriver.Edge(options=options, service=service)
        driver.set_page_load_timeout(30)  # 设置页面加载超时为30秒
    else:
        raise ValueError(f"不支持的浏览器类型: {browser}")
    return driver