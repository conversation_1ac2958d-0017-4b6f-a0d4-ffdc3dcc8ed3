#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的extract_articles_from_page函数
验证参考crawler.py的get_article_links和get_full_link逻辑
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_enhanced_extract_articles():
    """测试增强的文章提取功能"""
    print("=" * 60)
    print("测试增强的extract_articles_from_page函数")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        async with async_playwright() as p:
            # 启动浏览器
            browser, context, page = await PaginationHandler.launch_browser(p, headless=True)
            
            try:
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                # 测试URL - 使用一个有文章列表的政府网站
                test_url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
                
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                
                print("页面加载完成，开始提取文章...")
                
                # 使用增强的extract_articles_from_page函数
                articles_count = await handler.extract_articles_from_page(
                    list_container_selector=".search-result",  # 搜索结果容器
                    article_item_selector=".search-item a",    # 文章链接选择器
                    title_selector=None,                       # 使用默认标题提取
                    list_container_type="CSS",
                    article_item_type="CSS",
                    title_selector_type="CSS",
                    save_dir="test_articles",
                    page_title="测试页面",
                    classid="test",
                    base_url="http://www.gysrd.gov.cn",
                    url_mode="relative"
                )
                
                print(f"✅ 成功提取 {articles_count} 篇文章")
                
                # 获取提取到的文章
                all_articles = handler.get_all_articles()
                print(f"✅ 总共收集到 {len(all_articles)} 篇文章")
                
                # 显示前几篇文章的信息
                if all_articles:
                    print("\n前5篇文章信息:")
                    for i, article in enumerate(all_articles[:5]):
                        title, href, save_dir, page_title, page_url, classid = article
                        print(f"  {i+1}. 标题: {title[:50]}...")
                        print(f"     链接: {href}")
                        print(f"     完整URL: {'是' if href.startswith('http') else '否'}")
                        print()
                
                # 测试URL处理逻辑
                print("=" * 40)
                print("测试URL处理逻辑")
                print("=" * 40)
                
                test_cases = [
                    ("http://example.com/full", "完整URL"),
                    ("//example.com/protocol", "协议相对URL"),
                    ("/relative/path", "相对路径"),
                    ("relative/path", "相对路径"),
                    ("#anchor", "锚点链接")
                ]
                
                for test_href, desc in test_cases:
                    full_url = handler._get_full_link(
                        test_href, 
                        "http://www.gysrd.gov.cn/search", 
                        "http://www.gysrd.gov.cn", 
                        "relative"
                    )
                    print(f"  {desc}: {test_href} -> {full_url}")
                
                return True
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_onclick_extraction():
    """测试onclick属性的URL提取"""
    print("=" * 60)
    print("测试onclick属性URL提取")
    print("=" * 60)
    
    try:
        import re
        
        # 测试不同的onclick模式（参考crawler.py的逻辑）
        test_cases = [
            ("location='http://example.com/page1'", "http://example.com/page1"),
            ('window.location.href="http://example.com/page2"', "http://example.com/page2"),
            ("window.open('http://example.com/page3')", "http://example.com/page3"),
            ('redirectTo("http://example.com/page4")', "http://example.com/page4"),
            ("location.href = 'http://example.com/page5'", "http://example.com/page5"),
        ]
        
        patterns = [
            r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
            r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url')
            r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
            r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url')
        ]
        
        for onclick_text, expected_url in test_cases:
            extracted_url = None
            
            for pattern in patterns:
                match = re.search(pattern, onclick_text)
                if match:
                    extracted_url = match.group(1)
                    break
            
            if extracted_url == expected_url:
                print(f"✅ {onclick_text} -> {extracted_url}")
            else:
                print(f"❌ {onclick_text} -> 期望: {expected_url}, 实际: {extracted_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ onclick测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试增强的extract_articles_from_page函数...")
    print()
    
    success_count = 0
    total_tests = 2
    
    # 测试onclick提取
    if await test_onclick_extraction():
        success_count += 1
    print()
    
    # 测试增强的文章提取
    if await test_enhanced_extract_articles():
        success_count += 1
    print()
    
    print("=" * 60)
    print(f"测试完成！成功: {success_count}/{total_tests}")
    if success_count == total_tests:
        print("🎉 所有测试通过！extract_articles_from_page增强成功！")
        print("\n主要改进:")
        print("✅ 支持多个列表容器（参考crawler.py）")
        print("✅ 增强的链接提取（包括onclick属性解析）")
        print("✅ 完整的URL处理逻辑（参考get_full_link）")
        print("✅ 更robust的标题提取")
        print("✅ 与crawler.py兼容的数据格式")
    else:
        print(f"⚠️  有 {total_tests - success_count} 个测试失败")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
