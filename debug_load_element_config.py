#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试加载元素配置问题
"""

import json
import os

def check_pagination_config():
    """检查翻页配置"""
    print("=" * 60)
    print("检查翻页配置")
    print("=" * 60)
    
    config_files = [
        'pagination_config.json',
        'correct_pagination_config.json',
        'config.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n📁 配置文件: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查动态翻页配置
                if 'pagination_config' in config:
                    pagination_config = config['pagination_config']
                    print(f"  ✅ 找到 pagination_config")
                    print(f"    enabled: {pagination_config.get('enabled')}")
                    print(f"    pagination_type: {pagination_config.get('pagination_type')}")
                    print(f"    load_element_pattern: {pagination_config.get('load_element_pattern')}")
                    print(f"    load_indicator_selector: {pagination_config.get('load_indicator_selector')}")
                    
                    # 检查是否配置错误
                    load_element_pattern = pagination_config.get('load_element_pattern', '')
                    load_indicator_selector = pagination_config.get('load_indicator_selector', '')
                    
                    if '{n}' in load_indicator_selector:
                        print(f"  ❌ 错误配置: load_indicator_selector 包含 {{n}}: {load_indicator_selector}")
                        print(f"     应该设置在 load_element_pattern 中")
                    
                    if load_element_pattern and '{n}' in load_element_pattern:
                        print(f"  ✅ 正确配置: load_element_pattern = {load_element_pattern}")
                    elif not load_element_pattern and '{n}' in load_indicator_selector:
                        print(f"  🔧 建议修复: 将 load_indicator_selector 移动到 load_element_pattern")
                
                else:
                    print(f"  ❌ 未找到 pagination_config")
                    
            except Exception as e:
                print(f"  ❌ 读取配置文件出错: {e}")
        else:
            print(f"\n📁 配置文件不存在: {config_file}")

def create_correct_config():
    """创建正确的配置"""
    print("\n" + "=" * 60)
    print("创建正确的配置")
    print("=" * 60)
    
    correct_config = {
        "pagination_config": {
            "enabled": True,
            "pagination_type": "滚动翻页",
            "load_element_pattern": "#load{n}",  # 正确：用于 #load1, #load2, ... 模式
            "load_indicator_selector": None,     # 正确：不设置普通加载指示器
            "scroll_container_selector": "#largeData",
            "scroll_step": 1000,
            "scroll_delay": 2000,
            "scroll_timeout": 10000,
            "height_tolerance": 50,
            "max_loads": 15
        }
    }
    
    # 保存正确配置
    with open('fixed_pagination_config.json', 'w', encoding='utf-8') as f:
        json.dump(correct_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 已创建正确配置: fixed_pagination_config.json")
    print("\n配置内容:")
    print(json.dumps(correct_config, ensure_ascii=False, indent=2))

def test_config_logic():
    """测试配置逻辑"""
    print("\n" + "=" * 60)
    print("测试配置逻辑")
    print("=" * 60)
    
    # 模拟不同的配置情况
    test_cases = [
        {
            "name": "正确配置 - 加载元素模式",
            "config": {
                "pagination_type": "滚动翻页",
                "load_element_pattern": "#load{n}",
                "load_indicator_selector": None
            }
        },
        {
            "name": "错误配置 - 混淆了两个参数",
            "config": {
                "pagination_type": "滚动翻页",
                "load_element_pattern": "",
                "load_indicator_selector": "#load{n}"
            }
        },
        {
            "name": "传统滚动配置",
            "config": {
                "pagination_type": "滚动翻页",
                "load_element_pattern": "",
                "load_indicator_selector": ".loading"
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        config = case['config']
        
        pagination_type = config.get('pagination_type')
        load_element_pattern = config.get('load_element_pattern', '')
        load_indicator_selector = config.get('load_indicator_selector')
        
        print(f"  pagination_type: {pagination_type}")
        print(f"  load_element_pattern: '{load_element_pattern}'")
        print(f"  load_indicator_selector: {load_indicator_selector}")
        
        # 判断会使用哪种模式
        if pagination_type == '滚动翻页':
            if load_element_pattern and '{n}' in load_element_pattern:
                print(f"  → ✅ 将使用加载元素模式")
                print(f"     调用: scroll_pagination(load_element_pattern='{load_element_pattern}')")
            else:
                print(f"  → ✅ 将使用传统滚动模式")
                if load_indicator_selector and '{n}' in load_indicator_selector:
                    print(f"     ❌ 但是 load_indicator_selector 包含无效的 {{n}}: {load_indicator_selector}")
                    print(f"     🔧 这会导致CSS选择器解析错误")
                else:
                    print(f"     调用: scroll_pagination(load_indicator_selector='{load_indicator_selector}')")

def main():
    """主函数"""
    print("开始调试加载元素配置问题...")
    
    # 检查现有配置
    check_pagination_config()
    
    # 创建正确配置
    create_correct_config()
    
    # 测试配置逻辑
    test_config_logic()
    
    print("\n" + "=" * 60)
    print("问题诊断和解决方案")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("从日志看，系统使用了传统滚动模式，但是 load_indicator_selector")
    print("被设置为了 '#load{n}'，这是一个无效的CSS选择器。")
    
    print("\n💡 解决方案:")
    print("1. 确保 load_element_pattern 设置为 '#load{n}'")
    print("2. 确保 load_indicator_selector 为 None 或空")
    print("3. 确保 pagination_type 为 '滚动翻页'")
    
    print("\n🔧 正确的GUI配置:")
    print("在动态翻页设置中:")
    print("- 翻页类型: 滚动翻页")
    print("- 加载元素模式: #load{n}")
    print("- 加载指示器选择器: (留空)")
    
    print("\n🚀 下一步:")
    print("1. 检查GUI中的配置设置")
    print("2. 确保没有将 #load{n} 设置到错误的字段")
    print("3. 重新测试滚动翻页功能")

if __name__ == "__main__":
    main()
