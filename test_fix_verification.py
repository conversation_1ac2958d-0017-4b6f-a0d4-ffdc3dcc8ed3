#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复后的配置传递
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_passing_fix():
    """测试修复后的配置传递"""
    print("=" * 60)
    print("测试修复后的配置传递")
    print("=" * 60)
    
    # 模拟 get_current_config() 的返回值
    config_data = {
        "input_url": "https://www.shrd.gov.cn/n8347/n8378/index.html",
        "base_url": "https://www.shrd.gov.cn",
        "max_pages": "5",
        "list_container_selector": "#largeData",
        "list_container_type": "CSS",
        "article_item_selector": "li a",
        "article_item_type": "CSS",
        "title_selector": "h1",
        "title_selector_type": "CSS",
        "content_selectors": ".content",
        "content_type": "CSS",
        "date_selector": ".date",
        "date_selector_type": "CSS",
        "source_selector": ".source",
        "source_selector_type": "CSS",
        "page_suffix": "index_{n}.html",
        "page_suffix_start": 1,
        "dynamic_pagination_type": None,
        "pagination_config": {
            'pagination_type': '滚动翻页',
            'enabled': True,
            'scroll_container_selector': '#largeData',
            'scroll_step': 500,
            'scroll_delay': 1000,
            'max_scrolls': 20,
            'load_indicator_selector': '',
            'scroll_timeout': 10000,
            'height_tolerance': 50,
            'load_element_pattern': '#load{n}'
        },
        "url_mode": "absolute",
        "browser": "Firefox",
        "headless": True,
        "window_size": "1200,800",
        "page_load_strategy": "normal",
        "collect_links": True,
        "mode": "balance",
        "export_filename": None,
        "file_format": "CSV",
        "max_workers": 5,
        "retry": 3,
        "interval": 0.5,
        "classid": ""
    }
    
    print("模拟的 config_data:")
    print(f"  pagination_config: {config_data['pagination_config']}")
    
    # 模拟修复后的 start_crawler() 中的配置构建
    diver = {"headless": config_data["headless"]}
    if config_data["window_size"]:
        diver["window_size"] = config_data["window_size"]
    if config_data["page_load_strategy"]:
        diver["page_load_strategy"] = config_data["page_load_strategy"]
    
    crawler_config = {
        'input_url': config_data['input_url'],
        'base_url': config_data['base_url'],
        'max_pages': int(config_data['max_pages']) if config_data['max_pages'] else None,
        'list_container_selector': config_data['list_container_selector'],
        'list_container_type': config_data['list_container_type'],
        'article_item_selector': config_data['article_item_selector'],
        'article_item_type': config_data['article_item_type'],
        'title_selector': config_data['title_selector'],
        'title_selector_type': config_data['title_selector_type'],
        'content_selectors': [s.strip() for s in config_data['content_selectors'].split(';') if s.strip()],
        'content_type': config_data['content_type'],
        'date_selector': config_data['date_selector'],
        'date_selector_type': config_data['date_selector_type'],
        'source_selector': config_data['source_selector'],
        'source_selector_type': config_data['source_selector_type'],
        'page_suffix': config_data['page_suffix'],
        'page_suffix_start': config_data['page_suffix_start'],
        'url_mode': config_data['url_mode'],
        'browser': config_data['browser'],
        'diver': diver,
        'collect_links': config_data.get('collect_links', True),
        'mode': config_data.get('mode', 'balance'),
        'export_filename': config_data.get('export_filename', None),
        'file_format': config_data.get('file_format', 'CSV'),
        'max_workers': config_data.get('max_workers', 5),
        'retry': config_data.get('retry', 3),
        'interval': config_data.get('interval', 0.5),
        'classid': config_data.get('classid', ''),
        # 修复：添加 pagination_config
        'pagination_config': config_data.get('pagination_config', {}),
    }
    
    print("\n构建的 crawler_config:")
    print(f"  pagination_config: {crawler_config['pagination_config']}")
    
    # 模拟 CrawlerThread 中的判断逻辑
    pagination_config = crawler_config.get('pagination_config', {})
    enabled = pagination_config.get('enabled', False)
    pagination_type = pagination_config.get('pagination_type')
    
    print(f"\nCrawlerThread 中的判断:")
    print(f"  pagination_config: {pagination_config}")
    print(f"  enabled: {enabled}")
    print(f"  pagination_type: '{pagination_type}'")
    print(f"  pagination_type != '禁用动态翻页': {pagination_type != '禁用动态翻页'}")
    
    if enabled and pagination_type != '禁用动态翻页':
        print("  ✅ 将使用动态翻页模式")
        
        if pagination_type == '滚动翻页':
            print("  → 将调用滚动翻页功能")
            
            # 检查智能检测
            load_element_pattern = pagination_config.get('load_element_pattern', '')
            if load_element_pattern and '{n}' in load_element_pattern:
                print(f"  → 检测到加载元素模式: {load_element_pattern}")
                print("  → 智能检测将选择加载元素滚动模式")
            else:
                print("  → 智能检测将选择传统滚动模式")
        else:
            print(f"  → 翻页类型: {pagination_type}")
    else:
        print("  ❌ 将使用传统翻页模式")
        if not enabled:
            print(f"    原因: enabled = {enabled}")
        if pagination_type == '禁用动态翻页':
            print(f"    原因: pagination_type = '{pagination_type}'")

def test_before_and_after():
    """对比修复前后的差异"""
    print("\n" + "=" * 60)
    print("对比修复前后的差异")
    print("=" * 60)
    
    # 模拟修复前的配置（没有 pagination_config）
    config_before = {
        'input_url': 'https://www.shrd.gov.cn/n8347/n8378/index.html',
        'dynamic_pagination_type': None,
        # 注意：没有 pagination_config
    }
    
    # 模拟修复后的配置（有 pagination_config）
    config_after = {
        'input_url': 'https://www.shrd.gov.cn/n8347/n8378/index.html',
        'dynamic_pagination_type': None,
        'pagination_config': {
            'pagination_type': '滚动翻页',
            'enabled': True,
            'load_element_pattern': '#load{n}'
        }
    }
    
    print("修复前的配置:")
    pagination_config_before = config_before.get('pagination_config', {})
    enabled_before = pagination_config_before.get('enabled', False)
    print(f"  pagination_config: {pagination_config_before}")
    print(f"  enabled: {enabled_before}")
    print(f"  结果: {'✅ 动态翻页' if enabled_before else '❌ 传统翻页'}")
    
    print("\n修复后的配置:")
    pagination_config_after = config_after.get('pagination_config', {})
    enabled_after = pagination_config_after.get('enabled', False)
    pagination_type_after = pagination_config_after.get('pagination_type')
    print(f"  pagination_config: {pagination_config_after}")
    print(f"  enabled: {enabled_after}")
    print(f"  pagination_type: '{pagination_type_after}'")
    print(f"  结果: {'✅ 动态翻页' if (enabled_after and pagination_type_after != '禁用动态翻页') else '❌ 传统翻页'}")

def main():
    """主测试函数"""
    print("开始验证修复...")
    
    test_config_passing_fix()
    test_before_and_after()
    
    print("\n" + "=" * 60)
    print("修复验证结果")
    print("=" * 60)
    print("✅ 修复成功！")
    print("现在 pagination_config 会正确传递给 CrawlerThread")
    print("用户选择'滚动翻页'时，系统将使用动态翻页模式")
    print("\n请用户重新运行GUI测试:")
    print("1. 在动态翻页标签页选择'滚动翻页'")
    print("2. 配置相关参数（如 #load{n}）")
    print("3. 运行爬虫")
    print("4. 查看调试输出，应该显示'✅ 使用动态翻页模式'")

if __name__ == "__main__":
    main()
