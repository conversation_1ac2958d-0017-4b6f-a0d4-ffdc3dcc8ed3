#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试翻页问题的工具脚本
"""

import asyncio
from playwright.async_api import async_playwright
import sys
import os

async def debug_pagination_elements(url, selectors=None):
    """
    调试页面上的翻页元素
    """
    if selectors is None:
        selectors = [
            # 常见的下一页按钮选择器
            "a.next:not(.lose)",
            "a.page-link:has-text('Next')",
            "a.page-link:has-text('下一页')",
            "a[onclick*='page']",
            ".js_pageI:not(.cur)",
            ".pager a",
            "a:contains('下一页')",
            "a:contains('Next')",
            ".next-page",
            ".next",
            "a[title*='下一页']",
            "a[title*='Next']",
            ".pagination a",
            ".page-nav a",
            "a[href*='page']",
            "a[href*='Page']",
            # 数字翻页
            ".pagination .page-item:last-child a",
            ".page-numbers:last-child",
            # 更多可能的选择器
            "a[class*='next']",
            "a[id*='next']",
            "button[class*='next']",
            "button[onclick*='page']"
        ]

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 显示浏览器便于调试
        page = await browser.new_page()
        
        try:
            print(f"正在访问页面: {url}")
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(2000)  # 等待页面完全加载
            
            print(f"\n页面标题: {await page.title()}")
            print(f"页面URL: {page.url}")
            
            print("\n=== 搜索翻页元素 ===")
            found_elements = []
            
            for i, selector in enumerate(selectors):
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"\n✅ 找到选择器 [{i+1}]: {selector}")
                        for j, element in enumerate(elements):
                            try:
                                text = await element.text_content()
                                href = await element.get_attribute('href')
                                onclick = await element.get_attribute('onclick')
                                class_name = await element.get_attribute('class')
                                is_visible = await element.is_visible()
                                is_enabled = await element.is_enabled()
                                
                                element_info = {
                                    'selector': selector,
                                    'index': j,
                                    'text': text.strip() if text else '',
                                    'href': href,
                                    'onclick': onclick,
                                    'class': class_name,
                                    'visible': is_visible,
                                    'enabled': is_enabled
                                }
                                
                                found_elements.append(element_info)
                                
                                print(f"  元素 {j+1}:")
                                print(f"    文本: '{text.strip() if text else ''}' ")
                                print(f"    链接: {href}")
                                print(f"    点击事件: {onclick}")
                                print(f"    CSS类: {class_name}")
                                print(f"    可见: {is_visible}")
                                print(f"    启用: {is_enabled}")
                                
                            except Exception as e:
                                print(f"    获取元素信息时出错: {e}")
                                
                except Exception as e:
                    # 选择器无效或没找到元素，跳过
                    pass
            
            if not found_elements:
                print("❌ 没有找到任何翻页元素")
                
                # 尝试查找所有链接
                print("\n=== 查找所有链接 ===")
                all_links = await page.query_selector_all('a')
                print(f"页面总共有 {len(all_links)} 个链接")
                
                for i, link in enumerate(all_links[:20]):  # 只显示前20个
                    try:
                        text = await link.text_content()
                        href = await link.get_attribute('href')
                        if text and ('下一页' in text or 'next' in text.lower() or 
                                   '>' in text or '»' in text or 
                                   text.isdigit()):
                            print(f"  可能的翻页链接 {i+1}: '{text.strip()}' -> {href}")
                    except:
                        pass
            else:
                print(f"\n✅ 总共找到 {len(found_elements)} 个可能的翻页元素")
                
                # 推荐最佳选择器
                print("\n=== 推荐的选择器 ===")
                for element in found_elements:
                    if element['visible'] and element['enabled']:
                        if ('下一页' in element['text'] or 'next' in element['text'].lower() or
                            '>' in element['text'] or '»' in element['text']):
                            print(f"🎯 推荐: {element['selector']}")
                            print(f"   文本: '{element['text']}'")
                            break
            
            # 等待用户查看
            print(f"\n浏览器将保持打开状态30秒，请查看页面...")
            await page.wait_for_timeout(30000)
            
        except Exception as e:
            print(f"调试过程中出错: {e}")
        finally:
            await browser.close()

def main():
    if len(sys.argv) < 2:
        print("使用方法: python debug_pagination_issue.py <URL>")
        print("示例: python debug_pagination_issue.py https://example.com/news")
        return
    
    url = sys.argv[1]
    asyncio.run(debug_pagination_elements(url))

if __name__ == "__main__":
    main()
