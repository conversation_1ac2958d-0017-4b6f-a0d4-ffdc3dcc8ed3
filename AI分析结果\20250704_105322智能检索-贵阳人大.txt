list_container_selector=.js_basic_result_content
article_item_selector=.item.hasImg.is-news

======== 简要描述 =========
这是一个政府网站的搜索结果显示页面，结构清晰，采用静态HTML加载方式（非JS动态渲染）。列表容器包含多个文章条目，每个条目包含标题、描述、来源和时间等信息。翻页采用传统URL参数方式，当前显示72页（见分页控件最后一页数字），翻页时URL会保持相同路径仅修改page参数。

======= 容器分析结果 ======
- 主列表容器选择器为".js_basic_result_content"，该div包含所有搜索结果条目
- 单个文章条目选择器为".item.hasImg.is-news"，该class组合能精准匹配新闻类文章条目
- 文章标题位于a.title元素内（含span.type_title分类标签）
- 发布时间选择器为span.sourceTime
- 来源信息选择器为a.source
- 描述内容选择器为div.description > div.detail > p.js_text

（注：该页面搜索结果区域宽度840px，右侧有相关推荐栏，采集时应确保只采集左侧主内容区）