date_selector=meta[name="PubDate"], .attribute span:nth-child(3)
source_selector=meta[name="ContentSource"], .attribute span:nth-child(2)
content_selectors=.detailsMain .trs_editor_view
tilte_selector=meta[name="ArticleTitle"], .xw_title

======== 简要描述 =========
该网站是一个政府新闻类网站，结构清晰，采用传统的HTML布局。文章页面包含完整的元信息（标题、日期、来源等），正文内容位于特定容器内。特点包括：
1. 日期和来源信息同时存在于meta标签和页面可见元素中
2. 正文内容包含图片和格式化文本
3. 无分页内容，单页显示完整文章
4. 文章结构规范，信度较高

======= 容器分析结果 ======
date_selector=meta[name="PubDate"], .attribute span:nth-child(3)  # 双重保障，优先取meta标签
source_selector=meta[name="ContentSource"], .attribute span:nth-child(2)  # 同上
content_selectors=.detailsMain .trs_editor_view  # 正文专用容器，包含完整内容和图片
tilte_selector=meta[name="ArticleTitle"], .xw_title  # 标题双重保障