date_selector=.date
source_selector=.text-field-head span:first-child + span
content_selectors=.text-field-body
tilte_selector=.title

======== 简要描述 =========
这是一个政府新闻网站，结构清晰规范，采用传统静态页面布局。正文内容集中在.text-field-body容器内，包含规范的段落和图片。页面没有分页，单页显示完整内容。所有选择器定位准确度高，基于class和层级关系定位。

======= 容器分析结果 ======
date_selector=.date
source_selector=.text-field-head span:first-child + span
content_selectors=.text-field-body
tilte_selector=.title

解释说明：
1. 日期选择器定位到class="date"的元素
2. 来源选择器通过相邻选择器定位到"来源："文字后的span
3. 正文选择器直接使用包含所有内容的容器class
4. 标题选择器定位到class="title"的元素
这种配置能准确抓取结构化内容，且具有较好的容错性。