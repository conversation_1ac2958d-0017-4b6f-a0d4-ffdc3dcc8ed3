date_selector=.i-2
source_selector=.i-1
content_selectors=.cb p
tilte_selector=.ch h2

======== 简要描述 =========
这是一个政府新闻类网站，结构清晰规范。页面为单页形式，无分页内容。正文容器明确，包含标题、来源、日期和正文内容等标准新闻元素。日期和来源信息位于同一摘要栏内，通过不同class区分。正文内容全部包含在.cb容器的<p>标签中。

======= 容器分析结果 ======
date_selector=.i-2 // 日期所在的class容器
source_selector=.i-1 // 来源所在的class容器
content_selectors=.cb p // 正文内容所在的容器及其段落标签
tilte_selector=.ch h2 // 标题所在的容器和h2标签

建议采集配置说明：
1. 该站结构规范，所有关键信息都有明确的class标识
2. 正文内容建议采集.cb容器下的所有<p>标签，可保留完整格式
3. 无需处理分页，单页包含完整内容
4. 可额外采集.summary中的分享信息（如需要）
5. 图片内容也包含在.cb容器的<p>标签中，会一并采集