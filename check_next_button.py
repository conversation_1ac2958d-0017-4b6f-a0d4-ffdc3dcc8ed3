#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查网站的下一页按钮
"""

import asyncio
from playwright.async_api import async_playwright

async def check_next_button():
    """检查下一页按钮"""
    url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print(f"访问页面: {url}")
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            print("页面加载完成，开始检查翻页元素...")
            
            # 获取页面HTML内容
            html = await page.content()
            
            # 搜索包含翻页相关的HTML
            import re
            
            # 查找所有包含"下一页"、"next"、"page"等关键词的元素
            patterns = [
                r'<[^>]*下一页[^>]*>.*?</[^>]*>',
                r'<[^>]*next[^>]*>.*?</[^>]*>',
                r'<[^>]*page[^>]*>.*?</[^>]*>',
                r'<a[^>]*href[^>]*page[^>]*>.*?</a>',
                r'<[^>]*class[^>]*page[^>]*>.*?</[^>]*>',
                r'<[^>]*onclick[^>]*page[^>]*>.*?</[^>]*>'
            ]
            
            print("\n搜索翻页相关HTML元素:")
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
                if matches:
                    print(f"\n模式 {i+1}: {pattern}")
                    for j, match in enumerate(matches[:5]):  # 只显示前5个
                        print(f"  匹配 {j+1}: {match[:200]}...")
            
            # 检查常见的翻页选择器
            selectors = [
                "a.next:not(.lose)",
                ".next",
                "a.next",
                "[title*='下一页']",
                "[title*='下页']",
                "a[href*='page']",
                ".pagination a",
                ".page-next",
                ".next-page",
                "a[onclick*='page']",
                ".pager a",
                "[class*='page']",
                "a[class*='next']"
            ]
            
            print(f"\n检查常见翻页选择器:")
            for selector in selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"\n✅ 选择器 '{selector}': 找到 {len(elements)} 个元素")
                        for i, elem in enumerate(elements):
                            text = await elem.text_content()
                            href = await elem.get_attribute('href')
                            onclick = await elem.get_attribute('onclick')
                            class_name = await elem.get_attribute('class')
                            disabled = await elem.is_disabled()
                            visible = await elem.is_visible()
                            
                            print(f"  元素 {i+1}:")
                            print(f"    文本: '{text.strip() if text else ''}'")
                            print(f"    href: {href}")
                            print(f"    onclick: {onclick}")
                            print(f"    class: {class_name}")
                            print(f"    禁用: {disabled}")
                            print(f"    可见: {visible}")
                    else:
                        print(f"❌ 选择器 '{selector}': 未找到")
                except Exception as e:
                    print(f"❌ 选择器 '{selector}': 检查出错 - {e}")
            
            # 检查页面底部的分页区域
            print(f"\n检查页面底部分页区域:")
            try:
                # 滚动到页面底部
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(2000)
                
                # 截图保存分页区域
                await page.screenshot(path="pagination_area.png", full_page=True)
                print("已保存页面截图: pagination_area.png")
                
                # 查找分页容器
                pagination_containers = [
                    ".pagination",
                    ".pager",
                    ".page-nav",
                    "[class*='page']",
                    ".page-list"
                ]
                
                for container in pagination_containers:
                    try:
                        elements = await page.query_selector_all(container)
                        if elements:
                            print(f"\n✅ 分页容器 '{container}': 找到 {len(elements)} 个")
                            for i, elem in enumerate(elements):
                                html_content = await elem.inner_html()
                                print(f"  容器 {i+1} HTML: {html_content[:300]}...")
                    except Exception as e:
                        print(f"❌ 分页容器 '{container}': 检查出错 - {e}")
                        
            except Exception as e:
                print(f"检查分页区域出错: {e}")
            
            # 等待用户观察
            print(f"\n等待30秒供手动检查...")
            await page.wait_for_timeout(30000)
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(check_next_button())
