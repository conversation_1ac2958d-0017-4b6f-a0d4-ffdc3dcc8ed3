#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一的滚动翻页函数
"""

import sys
import os
import asyncio
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PaginationHandler import PaginationHandler, launch_browser
from playwright.async_api import async_playwright

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_unified_scroll_pagination():
    """测试统一的滚动翻页函数"""
    print("=" * 60)
    print("测试统一的滚动翻页函数")
    print("=" * 60)
    
    async with async_playwright() as p:
        # 启动浏览器
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 测试网站
            test_url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
            print(f"访问测试网站: {test_url}")
            
            await page.goto(test_url, wait_until="networkidle", timeout=30000)
            await page.wait_for_timeout(3000)
            
            # 准备文章提取配置
            extract_config = {
                'list_container_selector': 'body',
                'article_item_selector': "a[href*='/n8347/']",
                'title_selector': '',
                'save_dir': '测试结果',
                'page_title': '统一滚动翻页测试',
                'url_mode': 'relative'
            }
            
            print("\n" + "=" * 40)
            print("测试1: 加载元素模式 (#load{n})")
            print("=" * 40)
            
            # 测试加载元素模式
            load_result = await handler.scroll_pagination(
                scroll_container_selector="body",
                scroll_step=1000,
                scroll_delay=2000,
                max_scrolls=15,
                load_element_pattern="#load{n}",  # 启用加载元素模式
                max_loads=15,
                extract_articles_config=extract_config
            )
            
            print(f"✅ 加载元素模式完成，处理了 {load_result} 次加载")
            
            # 获取收集到的文章
            all_articles = handler.get_all_articles()
            print(f"✅ 总共收集到 {len(all_articles)} 篇文章")
            
            # 显示前5篇文章
            print("\n前5篇文章:")
            for i, article in enumerate(all_articles[:5]):
                print(f"  {i+1}. {article.get('title', '无标题')} - {article.get('url', '无链接')}")
            
            print("\n" + "=" * 40)
            print("测试2: 传统滚动模式")
            print("=" * 40)
            
            # 重新访问页面进行传统模式测试
            await page.goto("https://example.com", wait_until="networkidle")
            await page.wait_for_timeout(2000)
            
            # 清空之前的文章
            handler.all_articles = []
            
            # 测试传统滚动模式（不使用load_element_pattern）
            traditional_result = await handler.scroll_pagination(
                scroll_container_selector="body",
                scroll_step=500,
                scroll_delay=1000,
                max_scrolls=5,
                height_tolerance=50
                # 不设置 load_element_pattern，使用传统模式
            )
            
            print(f"✅ 传统滚动模式完成，滚动了 {traditional_result} 次")
            
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            # 关闭浏览器
            await context.close()
            await browser.close()

def test_function_parameters():
    """测试函数参数兼容性"""
    print("\n" + "=" * 60)
    print("测试函数参数兼容性")
    print("=" * 60)
    
    # 模拟不同的参数组合
    test_cases = [
        {
            "name": "加载元素模式参数",
            "params": {
                "load_element_pattern": "#load{n}",
                "max_loads": 10,
                "scroll_delay": 2000,
                "scroll_step": 800,
                "extract_articles_config": {"test": "config"}
            }
        },
        {
            "name": "传统滚动模式参数",
            "params": {
                "scroll_container_selector": "body",
                "scroll_step": 1000,
                "scroll_delay": 1000,
                "max_scrolls": 20,
                "load_indicator_selector": ".loading",
                "scroll_timeout": 10000,
                "height_tolerance": 50
            }
        },
        {
            "name": "混合参数（应该优先使用加载元素模式）",
            "params": {
                "scroll_container_selector": "body",
                "scroll_step": 800,
                "scroll_delay": 1500,
                "max_scrolls": 15,
                "load_element_pattern": "#load{n}",  # 有这个参数，应该使用加载元素模式
                "max_loads": 12,
                "extract_articles_config": {"test": "config"}
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        params = case['params']
        
        # 判断会使用哪种模式
        if params.get('load_element_pattern') and '{n}' in params.get('load_element_pattern', ''):
            print("  ✅ 将使用加载元素模式")
            print(f"    加载元素模式: {params.get('load_element_pattern')}")
            print(f"    最大加载次数: {params.get('max_loads', 50)}")
        else:
            print("  ✅ 将使用传统滚动模式")
            print(f"    滚动容器: {params.get('scroll_container_selector', 'body')}")
            print(f"    最大滚动次数: {params.get('max_scrolls', 20)}")
        
        print(f"    滚动步长: {params.get('scroll_step', 1000)}px")
        print(f"    滚动延迟: {params.get('scroll_delay', 1000)}ms")

async def main():
    """主函数"""
    print("开始测试统一的滚动翻页函数...")
    
    # 测试参数兼容性
    test_function_parameters()
    
    # 测试实际功能
    await test_unified_scroll_pagination()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print("✅ 函数合并完成:")
    print("1. scroll_pagination 现在是统一的滚动翻页函数")
    print("2. 自动检测是否使用加载元素模式 (load_element_pattern)")
    print("3. 向后兼容传统滚动模式参数")
    print("4. 删除了重复的 scroll_until_load_stops 函数")
    
    print("\n🔧 使用方法:")
    print("- 加载元素模式: 设置 load_element_pattern='#load{n}'")
    print("- 传统滚动模式: 不设置 load_element_pattern 或设置为空")
    print("- GUI 已更新为使用统一函数")
    
    print("\n🚀 现在可以测试GUI功能!")

if __name__ == "__main__":
    asyncio.run(main())
