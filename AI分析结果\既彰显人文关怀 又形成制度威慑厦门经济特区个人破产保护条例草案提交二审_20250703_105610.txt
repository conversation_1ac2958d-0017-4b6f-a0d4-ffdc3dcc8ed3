date_selector=.i-2
source_selector=.i-1
content_selectors=.TRS_Editor
tilte_selector=.ch h2

======== 简要描述 =========
该页面为政府新闻网站的标准文章页，具有以下特点：
1. 结构清晰，包含标题、来源、日期和正文等标准新闻元素
2. 正文内容位于.TRS_Editor容器内，包含多个段落
3. 单页文章，无分页内容
4. 日期和来源信息位于.summary容器内的特定div中
5. 标题位于.ch容器下的h2标签

======= 容器分析结果 ======
定位容器置信度高，所有关键信息都有明确的CSS选择器定位：
- 标题直接通过h2标签定位
- 日期和来源通过特定class的div定位
- 正文内容通过专用编辑器容器定位

建议采集配置：
1. 使用上述CSS选择器定位各元素
2. 注意处理正文中的特殊格式（如段落样式）
3. 无需处理分页（单页文章）
4. 可忽略页面底部的分享和附件等非核心内容