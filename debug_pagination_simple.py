#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单调试动态翻页问题
检查max_pages和翻页按钮
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def debug_simple_pagination():
    """简单调试翻页问题"""
    print("=" * 60)
    print("简单调试动态翻页问题")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        async with async_playwright() as p:
            # 启动浏览器（非无头模式）
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                # 测试URL
                test_url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
                
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                print("页面加载完成")
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                # 检查下一页按钮
                next_button_selector = "a.next:not(.lose)"
                print(f"\n检查下一页按钮选择器: {next_button_selector}")
                
                try:
                    # 查找所有匹配的元素
                    elements = await page.query_selector_all(next_button_selector)
                    print(f"找到 {len(elements)} 个匹配的元素")
                    
                    for i, elem in enumerate(elements):
                        text = await elem.text_content()
                        href = await elem.get_attribute('href')
                        disabled = await elem.is_disabled()
                        visible = await elem.is_visible()
                        class_attr = await elem.get_attribute('class')
                        
                        print(f"  元素{i+1}:")
                        print(f"    文本: '{text}'")
                        print(f"    链接: {href}")
                        print(f"    类名: {class_attr}")
                        print(f"    禁用: {disabled}")
                        print(f"    可见: {visible}")
                        print()
                        
                        # 如果元素可用，尝试点击
                        if visible and not disabled:
                            print(f"    尝试点击元素{i+1}...")
                            try:
                                await elem.click()
                                print("    ✅ 点击成功")
                                await page.wait_for_timeout(3000)
                                
                                # 检查URL是否改变
                                new_url = page.url
                                print(f"    新URL: {new_url}")
                                
                                if new_url != test_url:
                                    print("    ✅ URL已改变，翻页成功")
                                else:
                                    print("    ⚠️ URL未改变")
                                break
                            except Exception as e:
                                print(f"    ❌ 点击失败: {e}")
                        else:
                            print(f"    ❌ 元素不可点击（禁用={disabled}, 可见={visible}）")
                    
                    if not elements:
                        print("❌ 未找到下一页按钮")
                        
                        # 尝试其他可能的选择器
                        alternative_selectors = [
                            ".next",
                            "a.next",
                            "[title*='下一页']",
                            "a[href*='page']",
                            ".pagination a"
                        ]
                        
                        print("\n尝试其他选择器:")
                        for alt_selector in alternative_selectors:
                            try:
                                alt_elements = await page.query_selector_all(alt_selector)
                                if alt_elements:
                                    print(f"  ✅ {alt_selector}: 找到 {len(alt_elements)} 个元素")
                                    for j, alt_elem in enumerate(alt_elements):
                                        alt_text = await alt_elem.text_content()
                                        alt_href = await alt_elem.get_attribute('href')
                                        print(f"    元素{j+1}: '{alt_text}' -> {alt_href}")
                                else:
                                    print(f"  ❌ {alt_selector}: 未找到")
                            except Exception as e:
                                print(f"  ❌ {alt_selector}: 检查出错 - {e}")
                
                except Exception as e:
                    print(f"❌ 检查下一页按钮出错: {e}")
                
                # 测试不同的max_pages值
                print(f"\n测试不同的max_pages值:")
                test_max_pages = [1, 2, 3, 5]
                
                for max_pages in test_max_pages:
                    print(f"\n测试 max_pages = {max_pages}")
                    
                    # 重新加载页面
                    await page.goto(test_url, timeout=30000)
                    await page.wait_for_load_state('networkidle', timeout=10000)
                    
                    try:
                        # 测试翻页（不提取文章，只测试翻页逻辑）
                        pages_processed = await handler.click_pagination(
                            next_button_selector=next_button_selector,
                            max_pages=max_pages,
                            timeout=5000,
                            wait_after_click=1000,
                            disabled_check=True,
                            extract_articles_config=None  # 不提取文章
                        )
                        
                        print(f"  结果: 处理了 {pages_processed} 页")
                        
                        if pages_processed >= max_pages:
                            print(f"  ✅ 达到最大页数限制")
                        elif pages_processed == 1:
                            print(f"  ⚠️ 只处理了1页，可能是翻页按钮问题")
                        else:
                            print(f"  ⚠️ 处理了 {pages_processed} 页，少于预期的 {max_pages} 页")
                            
                    except Exception as e:
                        print(f"  ❌ 翻页测试失败: {e}")
                
                # 等待观察
                print("\n等待10秒供观察...")
                await page.wait_for_timeout(10000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("开始简单调试动态翻页问题...")
    await debug_simple_pagination()
    print("调试完成！")

if __name__ == "__main__":
    asyncio.run(main())
