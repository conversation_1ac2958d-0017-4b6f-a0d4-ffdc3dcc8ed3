#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试配置流程问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pagination_config():
    """测试翻页配置生成"""
    print("=" * 60)
    print("测试翻页配置生成")
    print("=" * 60)
    
    # 模拟用户选择"滚动翻页"的配置
    pagination_type = "滚动翻页"
    
    # 模拟 get_pagination_config() 方法的逻辑
    config = {
        'pagination_type': pagination_type,
        'enabled': pagination_type != "禁用动态翻页"
    }
    
    # 添加滚动翻页的具体配置
    if pagination_type == "滚动翻页":
        config.update({
            'scroll_container_selector': '#largeData',
            'scroll_step': 500,
            'scroll_delay': 1000,
            'max_scrolls': 20,
            'load_indicator_selector': '',
            'scroll_timeout': 10000,
            'height_tolerance': 50,
            'load_element_pattern': '#load{n}'
        })
    
    print("生成的 pagination_config:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 测试条件判断
    enabled = config.get('enabled', False)
    pagination_type_check = config.get('pagination_type') != '禁用动态翻页'
    
    print(f"\n条件判断:")
    print(f"  enabled: {enabled}")
    print(f"  pagination_type: {config.get('pagination_type')}")
    print(f"  pagination_type != '禁用动态翻页': {pagination_type_check}")
    print(f"  最终条件: {enabled and pagination_type_check}")
    
    if enabled and pagination_type_check:
        print("  ✅ 应该使用动态翻页模式")
    else:
        print("  ❌ 将使用传统翻页模式")
        if not enabled:
            print(f"    原因: enabled = {enabled}")
        if not pagination_type_check:
            print(f"    原因: pagination_type = '{config.get('pagination_type')}' 等于 '禁用动态翻页'")
    
    return config

def test_config_passing():
    """测试配置传递"""
    print("\n" + "=" * 60)
    print("测试配置传递")
    print("=" * 60)
    
    # 模拟完整的配置
    full_config = {
        "input_url": "https://www.shrd.gov.cn/n8347/n8378/index.html",
        "base_url": "https://www.shrd.gov.cn",
        "max_pages": "5",
        "list_container_selector": "#largeData",
        "article_item_selector": "li a",
        "title_selectors": ["h1", ".title"],
        "content_selectors": [".content"],
        "date_selectors": [".date"],
        "source_selectors": [".source"],
        "pagination_config": {
            'pagination_type': '滚动翻页',
            'enabled': True,
            'scroll_container_selector': '#largeData',
            'scroll_step': 500,
            'scroll_delay': 1000,
            'max_scrolls': 20,
            'load_indicator_selector': '',
            'scroll_timeout': 10000,
            'height_tolerance': 50,
            'load_element_pattern': '#load{n}'
        }
    }
    
    print("完整配置:")
    for key, value in full_config.items():
        if key == 'pagination_config':
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    # 模拟 CrawlerThread 中的逻辑
    pagination_config = full_config.get('pagination_config', {})
    enabled = pagination_config.get('enabled', False)
    pagination_type = pagination_config.get('pagination_type')
    
    print(f"\nCrawlerThread 中的判断:")
    print(f"  pagination_config: {pagination_config}")
    print(f"  enabled: {enabled}")
    print(f"  pagination_type: {pagination_type}")
    print(f"  pagination_type != '禁用动态翻页': {pagination_type != '禁用动态翻页'}")
    
    if enabled and pagination_type != '禁用动态翻页':
        print("  ✅ 将使用动态翻页模式")
        
        if pagination_type == '滚动翻页':
            print("  → 将调用滚动翻页功能")
            
            # 检查加载元素模式
            load_element_pattern = pagination_config.get('load_element_pattern', '')
            if load_element_pattern and '{n}' in load_element_pattern:
                print(f"  → 检测到加载元素模式: {load_element_pattern}")
                print("  → 智能检测将选择加载元素滚动模式")
            else:
                print("  → 智能检测将选择传统滚动模式")
        else:
            print(f"  → 翻页类型: {pagination_type}")
    else:
        print("  ❌ 将使用传统翻页模式")
        if not enabled:
            print(f"    原因: enabled = {enabled}")
        if pagination_type == '禁用动态翻页':
            print(f"    原因: pagination_type = '{pagination_type}'")

def test_gui_component_values():
    """测试GUI组件值"""
    print("\n" + "=" * 60)
    print("测试GUI组件可能的问题")
    print("=" * 60)
    
    # 可能的问题场景
    test_cases = [
        {
            "name": "正常情况 - 用户选择滚动翻页",
            "pagination_type_combo_text": "滚动翻页",
            "expected_enabled": True,
            "expected_type": "滚动翻页"
        },
        {
            "name": "问题情况1 - 组件返回空值",
            "pagination_type_combo_text": "",
            "expected_enabled": False,
            "expected_type": ""
        },
        {
            "name": "问题情况2 - 组件返回None",
            "pagination_type_combo_text": None,
            "expected_enabled": False,
            "expected_type": None
        },
        {
            "name": "问题情况3 - 用户选择了禁用",
            "pagination_type_combo_text": "禁用动态翻页",
            "expected_enabled": False,
            "expected_type": "禁用动态翻页"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试案例: {test_case['name']}")
        print("-" * 40)
        
        pagination_type = test_case['pagination_type_combo_text']
        
        # 模拟 get_pagination_config() 的逻辑
        config = {
            'pagination_type': pagination_type,
            'enabled': pagination_type != "禁用动态翻页" if pagination_type else False
        }
        
        print(f"  pagination_type_combo.currentText(): {pagination_type}")
        print(f"  生成的配置: {config}")
        
        enabled = config.get('enabled', False)
        config_type = config.get('pagination_type')
        
        print(f"  enabled: {enabled}")
        print(f"  pagination_type: {config_type}")
        
        if enabled and config_type != '禁用动态翻页':
            print("  结果: ✅ 使用动态翻页")
        else:
            print("  结果: ❌ 使用传统翻页")

def main():
    """主测试函数"""
    print("开始调试配置流程问题...")
    
    test_pagination_config()
    test_config_passing()
    test_gui_component_values()
    
    print("\n" + "=" * 60)
    print("调试建议")
    print("=" * 60)
    
    print("1. 检查GUI中的翻页类型选择:")
    print("   - 确认用户选择了'滚动翻页'而不是'禁用动态翻页'")
    print("   - 检查 pagination_type_combo.currentText() 的返回值")
    
    print("\n2. 检查配置生成:")
    print("   - 确认 get_pagination_config() 正确生成了 enabled=True")
    print("   - 确认 pagination_type='滚动翻页'")
    
    print("\n3. 检查配置传递:")
    print("   - 确认 get_current_config() 包含正确的 pagination_config")
    print("   - 确认 start_crawler() 传递了完整的配置给 CrawlerThread")
    
    print("\n4. 运行GUI并查看调试输出:")
    print("   - 现在GUI会输出详细的调试信息")
    print("   - 查看 [DEBUG] 开头的日志信息")

if __name__ == "__main__":
    main()
