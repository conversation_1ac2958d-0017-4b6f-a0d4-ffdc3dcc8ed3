#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前的翻页配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from myconfig import Config<PERSON><PERSON><PERSON>

def check_current_pagination_config():
    """检查当前的翻页配置"""
    
    try:
        config_manager = ConfigManager()
        
        # 获取所有配置组
        groups = config_manager.get_groups()
        print("=== 当前配置组 ===")
        for group in groups:
            print(f"- {group}")

        # 获取当前配置组
        current_group = config_manager.current_group
        print(f"\n当前配置组: {current_group}")
        
        if current_group:
            # 获取配置
            config = config_manager.get_config(current_group)
            
            print(f"\n=== 基本配置 ===")
            print(f"输入URL: {config.get('input_url', '')}")
            print(f"基础URL: {config.get('base_url', '')}")
            print(f"最大页数: {config.get('max_pages', '')}")
            
            # 检查翻页配置
            pagination_config = config.get('pagination_config', {})
            print(f"\n=== 翻页配置 ===")
            
            if pagination_config:
                print(f"翻页类型: {pagination_config.get('pagination_type', '未设置')}")
                print(f"是否启用: {pagination_config.get('enabled', False)}")
                
                if pagination_config.get('pagination_type') == '点击翻页':
                    print(f"\n--- 点击翻页设置 ---")
                    print(f"下一页按钮选择器: '{pagination_config.get('next_button_selector', '')}'")
                    print(f"内容就绪选择器: '{pagination_config.get('content_ready_selector', '')}'")
                    print(f"最大翻页数: {pagination_config.get('max_pages', 10)}")
                    print(f"点击后等待时间: {pagination_config.get('wait_after_click', 1500)}ms")
                    print(f"超时时间: {pagination_config.get('timeout', 10000)}ms")
                    print(f"检查按钮禁用: {pagination_config.get('disabled_check', True)}")
                    
                elif pagination_config.get('pagination_type') == '滚动翻页':
                    print(f"\n--- 滚动翻页设置 ---")
                    print(f"滚动容器选择器: '{pagination_config.get('scroll_container_selector', '')}'")
                    print(f"滚动步长: {pagination_config.get('scroll_step', 500)}px")
                    print(f"滚动延迟: {pagination_config.get('scroll_delay', 1000)}ms")
                    print(f"最大滚动次数: {pagination_config.get('max_scrolls', 20)}")
                    
                elif pagination_config.get('pagination_type') == 'iframe翻页':
                    print(f"\n--- iframe翻页设置 ---")
                    print(f"iframe选择器: '{pagination_config.get('iframe_selector', '')}'")
                    print(f"iframe翻页类型: {pagination_config.get('iframe_pagination_type', 'click')}")
                    
            else:
                print("❌ 没有找到翻页配置")
            
            # 检查旧的动态翻页类型
            old_type = config.get('dynamic_pagination_type')
            if old_type:
                print(f"\n旧的动态翻页类型: {old_type}")
                
        else:
            print("❌ 没有当前配置组")
            
    except Exception as e:
        print(f"检查配置时出错: {e}")
        import traceback
        traceback.print_exc()

def suggest_common_selectors():
    """建议常见的翻页选择器"""
    
    print(f"\n=== 常见翻页选择器建议 ===")
    
    selectors = [
        {
            "name": "通用下一页",
            "selector": "a.next:not(.lose)",
            "description": "最常见的下一页按钮，排除禁用状态"
        },
        {
            "name": "包含'下一页'文本",
            "selector": "a:has-text('下一页')",
            "description": "查找包含'下一页'文本的链接"
        },
        {
            "name": "包含'>'符号",
            "selector": "a:has-text('>')",
            "description": "查找包含'>'符号的链接"
        },
        {
            "name": "分页导航",
            "selector": ".pagination a:last-child",
            "description": "分页导航中的最后一个链接"
        },
        {
            "name": "JS翻页按钮",
            "selector": "a[onclick*='page']",
            "description": "包含翻页JavaScript的链接"
        },
        {
            "name": "数字翻页",
            "selector": ".js_pageI:not(.cur)",
            "description": "数字翻页按钮，排除当前页"
        }
    ]
    
    for i, sel in enumerate(selectors, 1):
        print(f"{i}. {sel['name']}")
        print(f"   选择器: {sel['selector']}")
        print(f"   说明: {sel['description']}")
        print()

if __name__ == "__main__":
    print("🔍 检查翻页配置工具")
    print("=" * 50)
    
    check_current_pagination_config()
    suggest_common_selectors()
    
    print("\n💡 建议:")
    print("1. 如果下一页按钮选择器为空或不正确，请在GUI中更新")
    print("2. 使用调试工具检查页面上的实际翻页元素:")
    print("   python debug_pagination_issue.py <您的URL>")
    print("3. 常见问题:")
    print("   - 选择器不匹配页面实际结构")
    print("   - 按钮被JavaScript动态生成")
    print("   - 按钮在iframe中")
    print("   - 需要等待页面加载完成")
