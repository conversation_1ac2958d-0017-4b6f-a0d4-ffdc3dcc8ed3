list_container_selector=.contentsList
article_item_selector=.contentsList p

======== 简要描述 =========
这是一个典型的政府网站文章列表页，采用静态HTML结构。特点：
1. 列表容器清晰，使用.contentsList类包裹所有文章条目
2. 每篇文章用<p>标签包含，内含<span>标题和<em>日期
3. 分页规则明确，URL格式为index_[页码].jhtml
4. 当前页面显示总页数为8页（尾页链接到index_8.jhtml）
5. 翻页区域使用.digg类，包含标准分页控件

======= 容器分析结果 ======
list_container_selector=.contentsList
article_item_selector=.contentsList p

建议采集配置：
1. 条目选择：优先采集p标签内的span内容（标题）和em内容（日期）
2. 链接提取：从p标签的onclick属性提取完整URL（location='...'格式）
3. 翻页策略：通过分析.digg区域的分页链接自动遍历
4. 去重依据：建议使用URL中的文章ID（如/24670.jhtml部分）作为唯一标识

置信度说明：
- 列表容器定位置信度：95%（明确的class命名和唯一结构）
- 文章条目定位置信度：90%（标准p标签结构，但需注意可能包含分页控件）
- 翻页机制置信度：85%（标准分页模式，但需处理首页无后缀的特殊情况）


======== 简要描述 =========
这是一个政府新闻详情页，结构清晰规范。特点包括：
1. 标准化的头部导航和页脚
2. 正文区域有明确分隔线
3. 包含规范的发布日期和来源信息
4. 单页内容，无分页
定位容器置信度高，所有关键元素都有明确的class标识。

======= 容器分析结果 ======
date_selector=.date
source_selector=.text-field-head span:first-of-type
content_selectors=.text-field-body
tilte_selector=.title

建议采集配置说明：
1. 标题选择器定位到class="title"的元素
2. 日期选择class="date"的元素
3. 来源选择.text-field-head下第一个span相邻的文本
4. 正文内容选择class="text-field-body"的容器
5. 无需处理分页，所有内容都在单页展示
6. 可添加图片采集规则，获取正文中的img标签

这种配置能稳定采集到完整的文章内容，包括：
- 标准化的标题
- 规范的发布日期
- 文章来源信息
- 完整正文（含图片）
- 无多余干扰内容