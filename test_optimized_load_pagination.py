#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的加载元素翻页功能
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_optimized_load_pagination():
    """测试优化后的加载元素翻页功能"""
    print("=" * 60)
    print("测试优化后的加载元素翻页功能")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        # 测试URL
        test_url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
        
        async with async_playwright() as p:
            # 启动浏览器（非无头模式以便观察）
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                print("页面加载完成")
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                print("\n开始测试优化后的加载元素翻页功能...")
                print("新逻辑: 滚动 → 等待加载元素出现 → 等待加载完成 → 继续滚动 → 循环")
                
                # 配置文章提取参数 - 尝试更通用的选择器
                extract_config = {
                    'list_container_selector': 'body',  # 使用body作为容器
                    'article_item_selector': 'a[href*="/n8347/"]',  # 更精确的文章链接选择器
                    'title_selector': '',
                    'save_dir': '测试结果',
                    'page_title': '测试页面',
                    'classid': 'test'
                }
                
                # 测试优化后的滚动直到加载停止功能
                print("使用加载元素模式: #load{n}")
                print("优化逻辑:")
                print("- 持续滚动直到找到加载元素")
                print("- 等待加载完成后继续滚动")
                print("- 连续失败3次后停止")
                
                load_count = await handler.scroll_until_load_stops(
                    load_element_pattern="#load{n}",
                    max_loads=15,  # 增加最大加载次数
                    scroll_delay=1500,  # 稍微减少延迟
                    scroll_step=600,  # 减少滚动步长
                    extract_articles_config=extract_config
                )
                
                print(f"\n✅ 优化后的加载元素翻页测试完成")
                print(f"实际加载次数: {load_count}")
                
                # 获取收集到的文章
                all_articles = handler.get_all_articles()
                print(f"总共收集到 {len(all_articles)} 篇文章")
                
                # 显示前几篇文章的信息
                if all_articles:
                    print("\n收集到的文章:")
                    for i, article in enumerate(all_articles[:10]):
                        title = article.get('title', '无标题')
                        url = article.get('url', '无链接')
                        print(f"  {i+1}. {title[:50]}{'...' if len(title) > 50 else ''}")
                        print(f"      URL: {url}")
                else:
                    print("\n⚠️ 未收集到文章，可能需要调整选择器")
                
                # 等待观察
                print("\n等待20秒供观察...")
                await page.wait_for_timeout(20000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_with_different_selectors():
    """使用不同的选择器测试"""
    print("\n" + "=" * 60)
    print("使用不同选择器测试")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        test_url = "https://www.shrd.gov.cn/n8347/n8378/index.html"
        
        async with async_playwright() as p:
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                await page.wait_for_load_state('networkidle', timeout=15000)
                
                # 先检查页面结构
                print("\n检查页面结构...")
                
                # 检查可能的文章链接
                links = await page.query_selector_all('a')
                print(f"页面总链接数: {len(links)}")
                
                # 检查包含特定路径的链接
                article_links = await page.query_selector_all('a[href*="/n8347/"]')
                print(f"包含 /n8347/ 的链接数: {len(article_links)}")
                
                # 检查列表项
                list_items = await page.query_selector_all('li')
                print(f"列表项数: {len(list_items)}")
                
                # 检查是否有加载元素
                for i in range(1, 6):
                    load_element = await page.query_selector(f'#load{i}')
                    if load_element:
                        print(f"找到加载元素: #load{i}")
                    else:
                        print(f"未找到加载元素: #load{i}")
                        break
                
                await page.wait_for_timeout(5000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 选择器测试失败: {e}")

async def main():
    """主函数"""
    print("开始测试优化后的加载元素翻页功能...")
    
    # 测试页面结构
    await test_with_different_selectors()
    
    # 测试优化后的加载元素翻页
    await test_optimized_load_pagination()
    
    print("\n" + "=" * 60)
    print("优化总结")
    print("=" * 60)
    print("✅ 优化后的逻辑:")
    print("1. 持续滚动直到找到加载元素")
    print("2. 等待加载元素完全加载完成")
    print("3. 继续滚动触发下一次加载")
    print("4. 重复循环直到连续失败3次")
    print("5. 增加了容错机制和调试信息")
    
    print("\n🔧 关键改进:")
    print("- 每次滚动后都检查加载元素")
    print("- 找到元素后等待加载完成再继续")
    print("- 连续失败机制避免无限循环")
    print("- 更详细的日志输出")

if __name__ == "__main__":
    asyncio.run(main())
