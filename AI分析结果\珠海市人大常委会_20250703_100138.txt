根据页面内容分析，以下是定位容器的CSS选择器：

date_selector=.n_new_xx_bt span
source_selector=
content_selectors=.TRS_Editor
tilte_selector=.n_new_xx_bt

======== 简要描述 =========
这是一个政府网站的文章详情页，结构清晰规范。特点：
1. 标题和日期在同一容器内，日期用span标签包裹
2. 正文内容有专门的TRS_Editor类容器
3. 未明确显示文章来源
4. 单篇文章页面，无翻页需求
5. 容器定位准确度高，特别是正文和标题部分

======= 容器分析结果 ======
date_selector=.n_new_xx_bt span
source_selector=
content_selectors=.TRS_Editor
tilte_selector=.n_new_xx_bt

建议采集配置说明：
1. 标题和日期选择器准确可靠
2. 正文选择.TRS_Editor能获取完整内容
3. 文章来源未在页面显示，故留空
4. 该页面为单篇文章，无需配置翻页规则
5. 整体采集准确度预计可达95%以上