#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的翻页功能
"""

import sys
import os
import asyncio

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_enhanced_pagination():
    """测试增强的翻页功能"""
    print("=" * 60)
    print("测试增强的翻页功能")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        # 使用一个简单的测试URL（百度搜索结果页面，有明确的翻页）
        test_url = "https://www.baidu.com/s?wd=python&pn=0"
        
        async with async_playwright() as p:
            # 启动浏览器（非无头模式）
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                print(f"访问测试页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                print("页面加载完成")
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                print("\n开始测试翻页功能...")
                
                # 测试翻页（百度的下一页按钮选择器）
                pages_processed = await handler.click_pagination(
                    next_button_selector="a[aria-label='下一页']",  # 百度的下一页按钮
                    max_pages=3,  # 测试3页
                    timeout=10000,
                    wait_after_click=2000,
                    disabled_check=True,
                    extract_articles_config=None  # 不提取文章，只测试翻页
                )
                
                print(f"\n✅ 翻页测试完成，处理了 {pages_processed} 页")
                
                # 等待观察
                print("等待10秒供观察...")
                await page.wait_for_timeout(10000)
                
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_original_url():
    """测试原始URL的翻页功能"""
    print("\n" + "=" * 60)
    print("测试原始URL的翻页功能")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        import PaginationHandler
        
        # 原始URL
        test_url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
        
        async with async_playwright() as p:
            # 启动浏览器（非无头模式）
            browser, context, page = await PaginationHandler.launch_browser(p, headless=False)
            
            try:
                print(f"访问原始页面: {test_url}")
                await page.goto(test_url, timeout=30000)
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle', timeout=15000)
                print("页面加载完成")
                
                # 创建PaginationHandler实例
                handler = PaginationHandler.PaginationHandler(page)
                
                print("\n开始测试原始URL的翻页功能...")
                
                # 测试翻页
                pages_processed = await handler.click_pagination(
                    next_button_selector="a.next:not(.lose)",
                    max_pages=3,  # 测试3页
                    timeout=10000,
                    wait_after_click=2000,
                    disabled_check=True,
                    extract_articles_config=None  # 不提取文章，只测试翻页
                )
                
                print(f"\n✅ 原始URL翻页测试完成，处理了 {pages_processed} 页")
                
                # 等待观察
                print("等待15秒供观察...")
                await page.wait_for_timeout(15000)
                
            except Exception as e:
                print(f"❌ 原始URL访问失败: {e}")
                # 如果是超时，可能是网络问题，跳过这个测试
                if "Timeout" in str(e):
                    print("可能是网络问题，跳过原始URL测试")
                else:
                    import traceback
                    traceback.print_exc()
            finally:
                await browser.close()
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("开始测试增强的翻页功能...")
    
    # 先测试百度（确保翻页逻辑正常）
    await test_enhanced_pagination()
    
    # 再测试原始URL
    await test_original_url()
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
