#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻页修复的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.options import Options
import time

def test_pagination_selectors():
    """测试翻页选择器"""
    
    # 配置Firefox选项
    options = Options()
    options.add_argument('--headless')  # 无头模式
    
    driver = None
    try:
        driver = webdriver.Firefox(options=options)
        
        # 访问测试页面
        test_url = "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="
        print(f"访问页面: {test_url}")
        
        driver.get(test_url)
        time.sleep(3)  # 等待页面加载
        
        print(f"页面标题: {driver.title}")
        
        # 测试选择器
        selectors_to_test = [
            ('a.next:not(.lose)', '修复后的选择器'),
            ('.next:not(.lose)', '原始选择器'),
            ('.js_pageI:not(.cur)', '数字翻页选择器'),
            ("//a[contains(text(), '下一页')]", 'XPath选择器')
        ]
        
        print("\n=== 测试选择器 ===")
        
        for selector, description in selectors_to_test:
            try:
                if selector.startswith('//'):
                    # XPath选择器
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    # CSS选择器
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    print(f"✅ {description}: 找到 {len(elements)} 个元素")
                    for i, element in enumerate(elements[:3]):  # 只显示前3个
                        try:
                            text = element.text.strip()
                            is_displayed = element.is_displayed()
                            is_enabled = element.is_enabled()
                            class_attr = element.get_attribute('class')
                            
                            print(f"   元素 {i+1}: '{text}' (显示:{is_displayed}, 启用:{is_enabled}, 类:{class_attr})")
                            
                            # 如果是下一页按钮，尝试点击测试
                            if ('下一页' in text or 'next' in text.lower()) and is_displayed and is_enabled:
                                print(f"   🎯 这是一个有效的下一页按钮！")
                                
                        except Exception as e:
                            print(f"   获取元素信息失败: {e}")
                else:
                    print(f"❌ {description}: 未找到元素")
                    
            except Exception as e:
                print(f"❌ {description}: 选择器错误 - {e}")
        
        # 测试实际点击功能
        print(f"\n=== 测试点击功能 ===")
        
        try:
            # 使用修复后的选择器
            next_button = driver.find_element(By.CSS_SELECTOR, 'a.next:not(.lose)')
            if next_button.is_displayed() and next_button.is_enabled():
                print("✅ 找到下一页按钮，准备点击...")
                
                # 记录点击前的URL
                before_url = driver.current_url
                print(f"点击前URL: {before_url}")
                
                # 点击按钮
                next_button.click()
                time.sleep(3)  # 等待页面加载
                
                # 检查URL是否改变
                after_url = driver.current_url
                print(f"点击后URL: {after_url}")
                
                if before_url != after_url:
                    print("✅ 翻页成功！URL已改变")
                else:
                    print("⚠️  URL未改变，可能是JavaScript翻页")
                    
                    # 检查页面内容是否改变
                    try:
                        # 查找页码指示器
                        current_page_elements = driver.find_elements(By.CSS_SELECTOR, '.cur, .current, .active')
                        if current_page_elements:
                            current_page = current_page_elements[0].text
                            print(f"当前页码: {current_page}")
                            if current_page == '2':
                                print("✅ JavaScript翻页成功！已到第2页")
                            else:
                                print(f"⚠️  页码显示为: {current_page}")
                        else:
                            print("⚠️  无法确定当前页码")
                    except Exception as e:
                        print(f"检查页码时出错: {e}")
                        
            else:
                print("❌ 下一页按钮不可用")
                
        except Exception as e:
            print(f"❌ 点击测试失败: {e}")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    print("🔧 测试翻页选择器修复")
    print("=" * 50)
    
    test_pagination_selectors()
    
    print("\n💡 总结:")
    print("1. 如果修复后的选择器 'a.next:not(.lose)' 找到了元素，说明修复成功")
    print("2. 如果点击测试成功，说明翻页功能正常")
    print("3. 如果仍有问题，可能需要调整等待时间或处理JavaScript事件")
