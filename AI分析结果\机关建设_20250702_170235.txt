list_container_selector=.section_01
article_item_selector=.groupList

======== 简要描述 =========
这是一个政府网站的文章列表页，采用传统静态分页结构。特点：
1. 分页规则清晰，URL格式为index.jhtml（首页）和index_[页码].jhtml（后续页）
2. 当前显示总页数为18页（尾页链接到index_18.jhtml）
3. 文章列表容器为.section_01，每篇文章包裹在.groupList中
4. 文章元素包含标题（span标签）和日期（em标签），标题可点击跳转详情页
5. 分页控件包含标准首页/上一页/页码/下一页/尾页元素

======= 容器分析结果 ======
list_container_selector=.section_01  // 主列表容器，包含所有文章项
article_item_selector=.groupList    // 每篇文章的容器，包含标题和日期元素

建议采集配置：
1. 翻页规则：递增数字替换（index.jhtml→index_1.jhtml→index_2.jhtml）
2. 字段提取：
   - 标题：.groupList span
   - 日期：.groupList em 
   - 链接：.groupList p的onclick属性（需正则提取URL）
3. 停止条件：当采集到重复URL或到达index_18.jhtml时停止

（置信度：高，容器选择器明确且结构稳定）