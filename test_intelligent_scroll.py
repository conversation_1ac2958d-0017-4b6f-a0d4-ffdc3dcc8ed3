#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能滚动翻页功能
"""

import sys
import os
import asyncio
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PaginationHandler import PaginationHandler, launch_browser
from playwright.async_api import async_playwright

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_intelligent_detection():
    """测试智能检测功能"""
    print("=" * 60)
    print("测试智能滚动翻页检测")
    print("=" * 60)
    
    async with async_playwright() as p:
        # 启动浏览器
        browser, context, page = await launch_browser(p, headless=False)
        
        try:
            # 创建PaginationHandler实例
            handler = PaginationHandler(page)
            
            # 测试网站列表
            test_sites = [
                {
                    "name": "上海人大网站（应该检测为加载元素模式）",
                    "url": "https://www.shrd.gov.cn/n8347/n8378/index.html",
                    "expected_mode": "load_elements"
                },
                {
                    "name": "普通网站（应该检测为传统模式）",
                    "url": "https://example.com",
                    "expected_mode": "traditional"
                }
            ]
            
            for site in test_sites:
                print(f"\n" + "=" * 40)
                print(f"测试网站: {site['name']}")
                print(f"URL: {site['url']}")
                print("=" * 40)
                
                try:
                    # 访问网站
                    await page.goto(site['url'], wait_until="networkidle", timeout=30000)
                    await page.wait_for_timeout(3000)
                    
                    # 测试智能检测
                    detected_mode = await handler._detect_pagination_mode()
                    print(f"检测结果: {detected_mode}")
                    print(f"预期结果: {site['expected_mode']}")
                    
                    if detected_mode == site['expected_mode']:
                        print("✅ 检测正确!")
                    else:
                        print("❌ 检测结果与预期不符")
                    
                    # 测试实际的滚动翻页调用
                    print("\n测试实际滚动翻页调用...")
                    
                    # 准备文章提取配置
                    extract_config = {
                        'list_container_selector': 'body',
                        'article_item_selector': "a[href*='/n8347/']" if 'shrd.gov.cn' in site['url'] else "a",
                        'title_selector': '',
                        'save_dir': '智能检测测试',
                        'page_title': f'智能检测测试-{site["name"]}',
                        'url_mode': 'relative'
                    }
                    
                    # 调用智能滚动翻页（只测试1-2次，不要运行太久）
                    result = await handler.scroll_pagination(
                        scroll_container_selector="body",
                        scroll_step=1000,
                        scroll_delay=2000,
                        max_scrolls=2,  # 限制次数
                        max_loads=2,    # 限制次数
                        extract_articles_config=extract_config
                    )
                    
                    print(f"✅ 滚动翻页完成，处理了 {result} 次")
                    
                    # 获取收集到的文章
                    articles = handler.get_all_articles()
                    print(f"✅ 收集到 {len(articles)} 篇文章")
                    
                    # 清空文章列表，准备下一个测试
                    handler.clear_articles()
                    
                except Exception as e:
                    print(f"❌ 测试网站出错: {e}")
                    import traceback
                    traceback.print_exc()
                    
        except Exception as e:
            print(f"❌ 总体测试出错: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            # 关闭浏览器
            await context.close()
            await browser.close()

def test_detection_logic():
    """测试检测逻辑"""
    print("\n" + "=" * 60)
    print("测试检测逻辑")
    print("=" * 60)
    
    # 模拟不同的参数组合
    test_cases = [
        {
            "name": "明确指定加载元素模式",
            "load_element_pattern": "#load{n}",
            "expected": "load_elements"
        },
        {
            "name": "空的加载元素模式",
            "load_element_pattern": "",
            "expected": "需要进一步检测"
        },
        {
            "name": "无效的加载元素模式",
            "load_element_pattern": "#loading",
            "expected": "需要进一步检测"
        },
        {
            "name": "None加载元素模式",
            "load_element_pattern": None,
            "expected": "需要进一步检测"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        load_element_pattern = case['load_element_pattern']
        
        # 模拟检测逻辑
        if load_element_pattern and '{n}' in load_element_pattern:
            result = "load_elements"
            print(f"  ✅ 明确指定的加载元素模式: {load_element_pattern}")
        else:
            result = "需要进一步检测（URL检测、元素检测等）"
            print(f"  → 需要进一步检测")
        
        print(f"  结果: {result}")
        print(f"  预期: {case['expected']}")

async def main():
    """主函数"""
    print("开始测试智能滚动翻页功能...")
    
    # 测试检测逻辑
    test_detection_logic()
    
    # 测试实际检测功能
    await test_intelligent_detection()
    
    print("\n" + "=" * 60)
    print("智能检测总结")
    print("=" * 60)
    print("🎯 智能检测功能:")
    print("1. 优先检查明确指定的 load_element_pattern")
    print("2. 自动检测页面中的 #load1, #load2 等元素")
    print("3. 根据URL识别已知的加载元素网站")
    print("4. 默认使用传统滚动模式")
    
    print("\n✅ GUI简化:")
    print("- GUI只需要调用 scroll_pagination() 函数")
    print("- 不需要复杂的模式判断逻辑")
    print("- 所有参数都传递给函数，让它自动选择")
    
    print("\n🚀 使用方法:")
    print("pages_processed = await handler.scroll_pagination(")
    print("    scroll_container_selector='body',")
    print("    scroll_delay=2000,")
    print("    max_scrolls=20,")
    print("    load_element_pattern='#load{n}',  # 可选")
    print("    extract_articles_config=config")
    print(")")
    
    print("\n🔧 现在GUI代码更简洁了！")

if __name__ == "__main__":
    asyncio.run(main())
